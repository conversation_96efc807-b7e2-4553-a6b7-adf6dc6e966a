{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-AnimateGraphicMaterials-Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Assembly-GraphicMaterialOverride": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Assembly-AnimateGraphicMaterials-Editor.dll": {}}, "runtime": {"bin/placeholder/Assembly-AnimateGraphicMaterials-Editor.dll": {}}}, "Assembly-GraphicMaterialOverride/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Assembly-GraphicMaterialOverride.dll": {}}, "runtime": {"bin/placeholder/Assembly-GraphicMaterialOverride.dll": {}}}, "PPv2URPConverters/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/PPv2URPConverters.dll": {}}, "runtime": {"bin/placeholder/PPv2URPConverters.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Burst.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.Editor.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.Collections.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Collections": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.Editor.dll": {}}}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}}, "Unity.InputSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.dll": {}}}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.ForUI.dll": {}}}, "Unity.InternalAPIEditorBridge.007/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InternalAPIEditorBridge.007.dll": {}}, "runtime": {"bin/placeholder/Unity.InternalAPIEditorBridge.007.dll": {}}}, "Unity.InternalAPIEngineBridge.007/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.InternalAPIEngineBridge.007.dll": {}}, "runtime": {"bin/placeholder/Unity.InternalAPIEngineBridge.007.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Multiplayer.Center.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}}, "Unity.Performance.Profile-Analyzer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Performance.Profile-Analyzer.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Performance.Profile-Analyzer.Editor.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst.Editor": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.Settings.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Settings.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Settings.Editor.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Settings.Editor": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model": "1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll": {}}}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll": {}}, "runtime": {"bin/placeholder/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.Tutorials.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Tutorials.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.Tutorials.Core.dll": {}}}, "Unity.Tutorials.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.EditorCoroutines.Editor": "1.0.0", "Unity.InternalAPIEditorBridge.007": "1.0.0", "Unity.InternalAPIEngineBridge.007": "1.0.0", "Unity.Settings.Editor": "1.0.0", "Unity.Tutorials.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Tutorials.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Tutorials.Core.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.dll": {}}}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.CoreUtils.Editor.dll": {}}}, "Unity.XR.Hands/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Hands.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Hands.dll": {}}}, "Unity.XR.Hands.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.Hands": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Hands.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Hands.Editor.dll": {}}}, "Unity.XR.Hands.Samples.VisualizerSample/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Hands": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Hands.Samples.VisualizerSample.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Hands.Samples.VisualizerSample.dll": {}}}, "Unity.XR.Interaction.Toolkit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Hands": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.dll": {}}}, "Unity.XR.Interaction.Toolkit.Analytics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.CoreUtils.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Analytics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Analytics.Editor.dll": {}}}, "Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Interaction.Toolkit": "1.0.0", "Unity.XR.Interaction.Toolkit.Analytics.Editor": "1.0.0", "Unity.XR.Interaction.Toolkit.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.dll": {}}}, "Unity.XR.Interaction.Toolkit.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Interaction.Toolkit": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Editor.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.Hands/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Hands": "1.0.0", "Unity.XR.Interaction.Toolkit": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.Hands.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.Hands.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Interaction.Toolkit": "1.0.0", "Unity.XR.Interaction.Toolkit.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.Interaction.Toolkit": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Interaction.Toolkit": "1.0.0", "Unity.XR.Interaction.Toolkit.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.dll": {}}}, "Unity.XR.Management/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.dll": {}}}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Management.Editor.dll": {}}}, "Unity.XR.OpenXR/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.dll": {}}}, "Unity.XR.OpenXR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.XR.CoreUtils.Editor": "1.0.0", "Unity.XR.Management": "1.0.0", "Unity.XR.Management.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Editor.dll": {}}}, "Unity.XR.OpenXR.Features.ConformanceAutomation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.ConformanceAutomation.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.ConformanceAutomation.dll": {}}}, "Unity.XR.OpenXR.Features.MetaQuestSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.dll": {}}}, "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.Management.Editor": "1.0.0", "Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll": {}}}, "Unity.XR.OpenXR.Features.OculusQuestSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.dll": {}}}, "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll": {}}}, "Unity.XR.OpenXR.Features.RuntimeDebugger/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.dll": {}}}, "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.OpenXR": "1.0.0", "Unity.XR.OpenXR.Editor": "1.0.0", "Unity.XR.OpenXR.Features.RuntimeDebugger": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll": {}}}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.SpatialTracking.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.InputSystem": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityEngine.XR.LegacyInputHelpers": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.XR.LegacyInputHelpers.dll": {}}}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.SpatialTracking.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.SpatialTracking": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.XR.LegacyInputHelpers.dll": {}}}}}, "libraries": {"Assembly-AnimateGraphicMaterials-Editor/1.0.0": {"type": "project", "path": "Assembly-AnimateGraphicMaterials-Editor.csproj", "msbuildProject": "Assembly-AnimateGraphicMaterials-Editor.csproj"}, "Assembly-GraphicMaterialOverride/1.0.0": {"type": "project", "path": "Assembly-GraphicMaterialOverride.csproj", "msbuildProject": "Assembly-GraphicMaterialOverride.csproj"}, "PPv2URPConverters/1.0.0": {"type": "project", "path": "PPv2URPConverters.csproj", "msbuildProject": "PPv2URPConverters.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Burst.Editor/1.0.0": {"type": "project", "path": "Unity.Burst.Editor.csproj", "msbuildProject": "Unity.Burst.Editor.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.Collections.Editor/1.0.0": {"type": "project", "path": "Unity.Collections.Editor.csproj", "msbuildProject": "Unity.Collections.Editor.csproj"}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "path": "Unity.EditorCoroutines.Editor.csproj", "msbuildProject": "Unity.EditorCoroutines.Editor.csproj"}, "Unity.InputSystem/1.0.0": {"type": "project", "path": "Unity.InputSystem.csproj", "msbuildProject": "Unity.InputSystem.csproj"}, "Unity.InputSystem.ForUI/1.0.0": {"type": "project", "path": "Unity.InputSystem.ForUI.csproj", "msbuildProject": "Unity.InputSystem.ForUI.csproj"}, "Unity.InternalAPIEditorBridge.007/1.0.0": {"type": "project", "path": "Unity.InternalAPIEditorBridge.007.csproj", "msbuildProject": "Unity.InternalAPIEditorBridge.007.csproj"}, "Unity.InternalAPIEngineBridge.007/1.0.0": {"type": "project", "path": "Unity.InternalAPIEngineBridge.007.csproj", "msbuildProject": "Unity.InternalAPIEngineBridge.007.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Common.csproj", "msbuildProject": "Unity.Multiplayer.Center.Common.csproj"}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Editor.csproj", "msbuildProject": "Unity.Multiplayer.Center.Editor.csproj"}, "Unity.Performance.Profile-Analyzer.Editor/1.0.0": {"type": "project", "path": "Unity.Performance.Profile-Analyzer.Editor.csproj", "msbuildProject": "Unity.Performance.Profile-Analyzer.Editor.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Editor.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Editor.csproj"}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Runtime.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Runtime.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Editor.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Shaders.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Shaders.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.Settings.Editor/1.0.0": {"type": "project", "path": "Unity.Settings.Editor.csproj", "msbuildProject": "Unity.Settings.Editor.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.TestTools.CodeCoverage.Editor/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.csproj"}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection/1.0.0": {"type": "project", "path": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "msbuildProject": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.Tutorials.Core/1.0.0": {"type": "project", "path": "Unity.Tutorials.Core.csproj", "msbuildProject": "Unity.Tutorials.Core.csproj"}, "Unity.Tutorials.Core.Editor/1.0.0": {"type": "project", "path": "Unity.Tutorials.Core.Editor.csproj", "msbuildProject": "Unity.Tutorials.Core.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "Unity.XR.CoreUtils/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.csproj", "msbuildProject": "Unity.XR.CoreUtils.csproj"}, "Unity.XR.CoreUtils.Editor/1.0.0": {"type": "project", "path": "Unity.XR.CoreUtils.Editor.csproj", "msbuildProject": "Unity.XR.CoreUtils.Editor.csproj"}, "Unity.XR.Hands/1.0.0": {"type": "project", "path": "Unity.XR.Hands.csproj", "msbuildProject": "Unity.XR.Hands.csproj"}, "Unity.XR.Hands.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Hands.Editor.csproj", "msbuildProject": "Unity.XR.Hands.Editor.csproj"}, "Unity.XR.Hands.Samples.VisualizerSample/1.0.0": {"type": "project", "path": "Unity.XR.Hands.Samples.VisualizerSample.csproj", "msbuildProject": "Unity.XR.Hands.Samples.VisualizerSample.csproj"}, "Unity.XR.Interaction.Toolkit/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.csproj"}, "Unity.XR.Interaction.Toolkit.Analytics.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj"}, "Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj"}, "Unity.XR.Interaction.Toolkit.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Editor.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.Hands/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.Hands.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.Hands.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}, "Unity.XR.Management/1.0.0": {"type": "project", "path": "Unity.XR.Management.csproj", "msbuildProject": "Unity.XR.Management.csproj"}, "Unity.XR.Management.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Management.Editor.csproj", "msbuildProject": "Unity.XR.Management.Editor.csproj"}, "Unity.XR.OpenXR/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.csproj", "msbuildProject": "Unity.XR.OpenXR.csproj"}, "Unity.XR.OpenXR.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Editor.csproj"}, "Unity.XR.OpenXR.Features.ConformanceAutomation/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.ConformanceAutomation.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.ConformanceAutomation.csproj"}, "Unity.XR.OpenXR.Features.MetaQuestSupport/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.MetaQuestSupport.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj"}, "Unity.XR.OpenXR.Features.OculusQuestSupport/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.OculusQuestSupport.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj"}, "Unity.XR.OpenXR.Features.RuntimeDebugger/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.RuntimeDebugger.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.RuntimeDebugger.csproj"}, "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor/1.0.0": {"type": "project", "path": "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj", "msbuildProject": "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj"}, "UnityEditor.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEditor.SpatialTracking.csproj", "msbuildProject": "UnityEditor.SpatialTracking.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEditor.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEditor.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEditor.XR.LegacyInputHelpers.csproj"}, "UnityEngine.SpatialTracking/1.0.0": {"type": "project", "path": "UnityEngine.SpatialTracking.csproj", "msbuildProject": "UnityEngine.SpatialTracking.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}, "UnityEngine.XR.LegacyInputHelpers/1.0.0": {"type": "project", "path": "UnityEngine.XR.LegacyInputHelpers.csproj", "msbuildProject": "UnityEngine.XR.LegacyInputHelpers.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-AnimateGraphicMaterials-Editor >= 1.0.0", "Assembly-GraphicMaterialOverride >= 1.0.0", "PPv2URPConverters >= 1.0.0", "Unity.Burst >= 1.0.0", "Unity.Burst.Editor >= 1.0.0", "Unity.Collections >= 1.0.0", "Unity.Collections.Editor >= 1.0.0", "Unity.EditorCoroutines.Editor >= 1.0.0", "Unity.InputSystem >= 1.0.0", "Unity.InputSystem.ForUI >= 1.0.0", "Unity.InternalAPIEditorBridge.007 >= 1.0.0", "Unity.InternalAPIEngineBridge.007 >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.Multiplayer.Center.Common >= 1.0.0", "Unity.Multiplayer.Center.Editor >= 1.0.0", "Unity.Performance.Profile-Analyzer.Editor >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Editor.Shared >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared >= 1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime >= 1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary >= 1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Editor >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Shaders >= 1.0.0", "Unity.Rendering.LightTransport.Editor >= 1.0.0", "Unity.Rendering.LightTransport.Runtime >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.Searcher.Editor >= 1.0.0", "Unity.Settings.Editor >= 1.0.0", "Unity.ShaderGraph.Editor >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model >= 1.0.0", "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.Tutorials.Core >= 1.0.0", "Unity.Tutorials.Core.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "Unity.XR.CoreUtils >= 1.0.0", "Unity.XR.CoreUtils.Editor >= 1.0.0", "Unity.XR.Hands >= 1.0.0", "Unity.XR.Hands.Editor >= 1.0.0", "Unity.XR.Hands.Samples.VisualizerSample >= 1.0.0", "Unity.XR.Interaction.Toolkit >= 1.0.0", "Unity.XR.Interaction.Toolkit.Analytics.Editor >= 1.0.0", "Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor >= 1.0.0", "Unity.XR.Interaction.Toolkit.Editor >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.Hands >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor >= 1.0.0", "Unity.XR.Management >= 1.0.0", "Unity.XR.Management.Editor >= 1.0.0", "Unity.XR.OpenXR >= 1.0.0", "Unity.XR.OpenXR.Editor >= 1.0.0", "Unity.XR.OpenXR.Features.ConformanceAutomation >= 1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport >= 1.0.0", "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor >= 1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport >= 1.0.0", "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor >= 1.0.0", "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor >= 1.0.0", "UnityEditor.SpatialTracking >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEditor.XR.LegacyInputHelpers >= 1.0.0", "UnityEngine.SpatialTracking >= 1.0.0", "UnityEngine.UI >= 1.0.0", "UnityEngine.XR.LegacyInputHelpers >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}}