-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-define:UNITY_6000_1_6
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:USE_INPUT_SYSTEM_POSE_CONTROL
-define:UNITY_POST_PROCESSING_STACK_V2
-define:USE_STICK_CONTROL_THUMBSTICKS
-define:ENABLE_VR_MODULE
-define:ENABLE_XR_MODULE
-define:ENABLE_INPUT_SYSTEM_PACKAGE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Camera/CameraHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Camera/CameraSwitcher.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Camera/FreeCamera.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/BaseCommandBufer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/CommandBufferHelpers.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/ComputeCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/IBaseCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/IComputeCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/IRasterCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/IUnsafeCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/RasterCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/CommandBuffers/UnsafeCommandBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/CommandBufferPool.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/CommonStructs.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ComponentSingleton.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ConstantBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ContextContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/CoreAttributes.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/CoreProfileId.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/CoreUnsafeUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/DynamicArray.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/DynamicResolutionHandler.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/DynamicString.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/GlobalDynamicResolutionSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/IAdditionalData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/IVirtualTexturingEnabledRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ListBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ObjectPools.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/Observable.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ObservableList.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ReloadAttribute.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/ReloadGroupAttribute.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/RemoveRange.Extensions.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/SerializableEnum.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/SerializedDictionary.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Common/Swap.Extensions.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplaySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplaySettingsHDROutput.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplaySettingsPanel.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplaySettingsStats.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplaySettingsUI.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplaySettingsVolumes.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugDisplayStats.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugFrameTiming.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugManager.Actions.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugManager.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugManager.UIState.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugOverlay.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugShapes.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugUI.Containers.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugUI.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugUI.Fields.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugUI.Panel.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/DebugUpdater.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/FrameTiming/FrameTimeBottleneck.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/FrameTiming/FrameTimeSample.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/IDebugDisplaySettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/IDebugDisplaySettingsData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/IDebugDisplaySettingsPanel.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/IDebugDisplaySettingsQuery.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/IVolumeDebugSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/MousePositionDebug.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerBitField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerButton.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerCanvas.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerColor.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerEnumField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerEnumHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerFloatField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerFoldout.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerGroup.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerHBox.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerIndirectFloatField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerIndirectToggle.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerIntField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerMessageBox.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerObject.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerObjectList.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerObjectPopupField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerPanel.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerPersistentCanvas.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerProgressBar.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerRenderingLayerField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerRow.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerToggle.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerToggleHistory.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerUIntField.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerValue.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerValueTuple.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerVBox.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerVector2.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerVector3.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerVector4.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/DebugUIHandlerWidget.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/Prefabs/Scripts/UIFoldout.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/ProfilingScope.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/ShaderDebugPrintManager.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Debugging/VolumeDebugSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Deprecated.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Documentation.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Inputs/InputRegistering.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/IProbeVolumeEnabledRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeAdjustmentVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeBrickIndex.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeBrickPool.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeIndexOfIndices.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.Binding.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.Debug.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.ReflProbeNormalization.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeReferenceVolume.Streaming.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolume.Migration.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeBakingProcessSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeBakingSet.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeBakingSet.Editor.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeConstantRuntimeResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeGIContributor.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeGlobalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumePerSceneData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumePositioning.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeSceneData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeScratchBufferPool.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumesOptions.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ProbeVolumeStreamableAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/ProbeVolume/ShaderVariablesProbeVolumes.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lighting/SphericalHarmonics.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Lights/LightAnchor.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/LookDev/IDataProvider.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/PostProcessing/HDROutputDefines.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/PostProcessing/IPostProcessComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/PostProcessing/LensFlareCommonSRP.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/PostProcessing/LensFlareComponentSRP.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/PostProcessing/LensFlareDataSRP.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/PostProcessing/LensFlareOcclusionPermutation.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Compiler/CompilerContextData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Compiler/FixedAttachmentArray.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Compiler/NativePassCompiler.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Compiler/NativePassCompiler.Debug.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Compiler/PassesData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Compiler/ResourcesData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Debug/DebugDisplaySettingsRenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/Debug/RenderGraphDebugParams.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/IRenderGraphBuilder.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/IRenderGraphEnabledRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/IRenderGraphRecorder.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraph.Compiler.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraph.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraph.DebugData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphBuilder.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphBuilders.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphCompilationCache.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphDefaultResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphGlobalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphLogger.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphObjectPool.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphPass.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphPassType.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphProfileId.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResourceAccelerationStructure.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResourceBuffer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResourcePool.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResourceRegistry.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResourceRendererList.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphResourceTexture.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphUtilsBlit.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderGraph/RenderGraphUtilsResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderPipeline/ICloudBackground.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderPipeline/IVolumetricCloud.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderPipeline/RenderPipelineGlobalSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderPipeline/RenderPipelineGlobalSettingsUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderPipeline/RenderPipelineGraphicsSettingsContainer.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/RenderPipeline/RenderPipelineResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Settings/IDefaultVolumeProfileResource.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Settings/IDefaultVolumeProfileSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Settings/IncludeRenderPipelineAsset.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Settings/LightmapSamplingSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Settings/ShaderStrippingSetting.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/ShaderGenerator/ShaderGeneratorAttributes.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/ShaderLibrary/Sampling/Hammersley.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/STP/ISTPEnabledRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/STP/STP.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Stripping/IRenderPipelineGraphicsSettingsStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Stripping/IStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Stripping/RenderPipelineGraphicsSettingsStripper.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Stripping/RenderPipelineGraphicsSettingsStripperFetcher.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Stripping/RenderPipelineGraphicsSettingsStripperReport.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/BufferedRTHandleSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/DepthBits.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/MSAASamples.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/PowerOfTwoTextureAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/RTHandle.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/RTHandles.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/RTHandleSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/Texture2DAtlas.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Textures/TextureXR.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/ArrayExtensions.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/BatchRendererGroupGlobals.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/BitArray.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/Blitter.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/CameraCaptureBridge.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/ColorSpaceUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/ColorUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/CoreMatrixUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/CoreRenderPipelinePreferences.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/CoreUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/CullContextData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/DelegateUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/FSRUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/GPUPrefixSum/GPUPrefixSum.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/GPUPrefixSum/GPUPrefixSum.Data.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/GPUPrefixSum/GPUPrefixSum.ShaderIDs.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/GPUSort/GPUSort.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/GPUSort/GPUSort.Data.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/HableCurve.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/HaltonSequence.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/HashFNV1A32.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/HDROutputUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/LightUnitUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/MaterialQuality.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/MeshGizmo.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/ResourceReloader.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/SceneRenderPipeline.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/TextureCurve.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/TextureGradient.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Utilities/TileLayoutUtils.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/IVolume.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/KeyframeUtility.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/Volume.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeCollection.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeComponent.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeComponent.EditorOnly.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeDebugData.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeManager.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeParameter.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeProfile.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Volume/VolumeStack.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Vrs/Vrs.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Vrs/VrsLut.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Vrs/VrsRenderPipelineRuntimeResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Vrs/VrsResources.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/Vrs/VrsShaders.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRBuiltinShaderConstants.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRGraphicsAutomatedTests.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRLayout.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRLayoutStack.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRMirrorView.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XROcclusionMesh.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRPass.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRSRPSettings.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRSystem.cs"
"Library/PackageCache/com.unity.render-pipelines.core@a2ee32414adf/Runtime/XR/XRView.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"