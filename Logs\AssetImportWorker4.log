Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.6f1 (d64b1a599cad) revision 14043930'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'fr' Physical Memory: 32471 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-18T16:37:04Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
C:/Users/<USER>/Vr-Archi-Stage-01
-logFile
Logs/AssetImportWorker4.log
-srvPort
60187
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Vr-Archi-Stage-01
C:/Users/<USER>/Vr-Archi-Stage-01
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34524]  Target information:

Player connection [34524]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1622932966 [EditorId] 1622932966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34524]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1622932966 [EditorId] 1622932966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34524]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1622932966 [EditorId] 1622932966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34524]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1622932966 [EditorId] 1622932966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34524]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1622932966 [EditorId] 1622932966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34524] Host joined multi-casting on [***********:54997]...
Player connection [34524] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1521.37 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.6f1 (d64b1a599cad)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Vr-Archi-Stage-01/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 4060 Laptop GPU (ID=0x28a0)
    Vendor:          NVIDIA
    VRAM:            7957 MB
    App VRAM Budget: 7189 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56200
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.006046 seconds.
- Loaded All Assemblies, in 11.033 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.508 seconds
Domain Reload Profiling: 12533ms
	BeginReloadAssembly (1113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (1712ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (8122ms)
		LoadAssemblies (1136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (8092ms)
			TypeCache.Refresh (8086ms)
				TypeCache.ScanAssembly (4992ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1508ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1294ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (192ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (175ms)
			ProcessInitializeOnLoadMethodAttributes (832ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 25.701 seconds
Refreshing native plugins compatible for Editor in 4.91 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.329 seconds
Domain Reload Profiling: 27026ms
	BeginReloadAssembly (199ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (25412ms)
		LoadAssemblies (23782ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1749ms)
			TypeCache.Refresh (1694ms)
				TypeCache.ScanAssembly (1526ms)
			BuildScriptInfoCaches (40ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1329ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1205ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (930ms)
			ProcessInitializeOnLoadMethodAttributes (161ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 5.18 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 269 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6064 unused Assets / (7.9 MB). Loaded Objects now: 6948.
Memory consumption went from 198.9 MB to 191.0 MB.
Total: 8.338900 ms (FindLiveObjects: 0.522700 ms CreateObjectMapping: 0.425100 ms MarkObjects: 4.310700 ms  DeleteObjects: 3.078400 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.22 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5852 unused Assets / (8.0 MB). Loaded Objects now: 6633.
Memory consumption went from 155.9 MB to 147.9 MB.
Total: 10.589300 ms (FindLiveObjects: 0.593500 ms CreateObjectMapping: 0.490500 ms MarkObjects: 5.978200 ms  DeleteObjects: 3.524800 ms)

Prepare: number of updated asset objects reloaded= 16
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.760 seconds
Refreshing native plugins compatible for Editor in 7.49 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.974 seconds
Domain Reload Profiling: 1734ms
	BeginReloadAssembly (214ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (467ms)
		LoadAssemblies (331ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (233ms)
			TypeCache.Refresh (94ms)
				TypeCache.ScanAssembly (82ms)
			BuildScriptInfoCaches (122ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (975ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (804ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (546ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.65 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 74 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5917 unused Assets / (7.6 MB). Loaded Objects now: 6639.
Memory consumption went from 187.7 MB to 180.2 MB.
Total: 9.684500 ms (FindLiveObjects: 0.521200 ms CreateObjectMapping: 0.574900 ms MarkObjects: 4.669100 ms  DeleteObjects: 3.918100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.732 seconds
Refreshing native plugins compatible for Editor in 6.01 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.938 seconds
Domain Reload Profiling: 1668ms
	BeginReloadAssembly (206ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (318ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (217ms)
			TypeCache.Refresh (65ms)
				TypeCache.ScanAssembly (55ms)
			BuildScriptInfoCaches (136ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (938ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (787ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (131ms)
			ProcessInitializeOnLoadAttributes (509ms)
			ProcessInitializeOnLoadMethodAttributes (121ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 7.51 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 65 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5917 unused Assets / (8.2 MB). Loaded Objects now: 6642.
Memory consumption went from 213.3 MB to 205.1 MB.
Total: 10.999600 ms (FindLiveObjects: 0.667200 ms CreateObjectMapping: 0.388200 ms MarkObjects: 5.524700 ms  DeleteObjects: 4.416600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly DLL name is reserved for internal use: Assets/AR-House_Arcticture-unity-master/Library/PlayerDataCache/Android/Data/Managed/mscorlib.dll (did files generated by a build accidentally end up in your Assets/ folder?)
Assembly DLL name is reserved for internal use: Assets/AR-House_Arcticture-unity-master/Library/PlayerDataCache/Android/Data/Managed/UnityEngine.dll (did files generated by a build accidentally end up in your Assets/ folder?)
Refreshing native plugins compatible for Editor in 7.40 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 60 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6010 unused Assets / (10.9 MB). Loaded Objects now: 6695.
Memory consumption went from 208.8 MB to 197.8 MB.
Total: 11.522500 ms (FindLiveObjects: 0.518400 ms CreateObjectMapping: 0.318500 ms MarkObjects: 6.100200 ms  DeleteObjects: 4.584200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 472910.990077 seconds.
  path: Assets/XR
  artifactKey: Guid(22baeac49dfd54025859cd02808b06f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XR using Guid(22baeac49dfd54025859cd02808b06f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8ded686f0a5166b12c0f51ff3ca0e7e3') in 0.0154924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/XR/Settings
  artifactKey: Guid(e46a7475b581f492b93350cfd49fe84c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XR/Settings using Guid(e46a7475b581f492b93350cfd49fe84c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2dd8378d741ded23f186294301dc92e1') in 0.0005923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/XR/Settings/OpenXRPackageSettings.asset
  artifactKey: Guid(a9a6963505ddf7f4d886008c6dc86122) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XR/Settings/OpenXRPackageSettings.asset using Guid(a9a6963505ddf7f4d886008c6dc86122) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '36223dd79d186771631ce2ef56d1c3f8') in 0.0334256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.887790 seconds.
  path: Assets/XR/Loaders
  artifactKey: Guid(8abd0515267824d82915b19345730a06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XR/Loaders using Guid(8abd0515267824d82915b19345730a06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2ad3867e0cf1387b8197a6b13654c2e5') in 0.0007078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.025463 seconds.
  path: Assets/XR/Loaders/Oculus Loader.asset
  artifactKey: Guid(eb6d337c1678a97408eb0481002d9ae2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XR/Loaders/Oculus Loader.asset using Guid(eb6d337c1678a97408eb0481002d9ae2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c678dfc97f645d2ec2686d1dee4523a') in 0.0009834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/XR/Loaders/Open XR Loader No Pre Init.asset
  artifactKey: Guid(b95054c657380c840a65b692a1bc899c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/XR/Loaders/Open XR Loader No Pre Init.asset using Guid(b95054c657380c840a65b692a1bc899c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89708986c78068777205c784948b9dd5') in 0.0032739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 5.330599 seconds.
  path: Assets/VRTemplateAssets
  artifactKey: Guid(83b7bbecd9fac4db9824039903a1eea8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/VRTemplateAssets using Guid(83b7bbecd9fac4db9824039903a1eea8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e3bec2810a149d1a28bf23dacc230e1b') in 0.0006606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 32.193087 seconds.
  path: Assets/AR-House_Arcticture-unity-master
  artifactKey: Guid(05f9aabb6eed53f4a9726c5850decf4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master using Guid(05f9aabb6eed53f4a9726c5850decf4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c2ebee5bcd79377d78534dac9ddcb6ea') in 0.0005747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.575919 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Project/Scenes/MainScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Project/Scenes/MainScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '41ba4adc4567dde06640fb404b39e4f9') in 0.0005768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 25.137663 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Project/Scenes
  artifactKey: Guid(4f704ae4b4f98ae41a0bce26658850c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Project/Scenes using Guid(4f704ae4b4f98ae41a0bce26658850c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb97012d7475ba7878faa76ae7b7deed') in 0.0006138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.014921 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Project/Scenes/MainScene
  artifactKey: Guid(4f4f8671cc9104240b4fa3d0689b8741) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Project/Scenes/MainScene using Guid(4f4f8671cc9104240b4fa3d0689b8741) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dbb283bc69a97904c1d65ef79e7a1a5f') in 0.0005095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 11.900274 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets
  artifactKey: Guid(d642b17ccedbda342a51ce16ac7be016) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets using Guid(d642b17ccedbda342a51ce16ac7be016) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c9868bb274e9922a1c4384fb0904a492') in 0.0005629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.652549 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2
  artifactKey: Guid(7e7268e2950ddbb49ab8cc6f1bf02ee6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2 using Guid(7e7268e2950ddbb49ab8cc6f1bf02ee6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f65048acee10c6b273e901700e03b986') in 0.0004964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.010590 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab
  artifactKey: Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab using Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '90dc21602f0ca4a020f4f6b9dd0b49e8') in 1.2538185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2378

========================================================================
Received Import Request.
  Time since last request: 31.767599 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab
  artifactKey: Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab using Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ff0f634b62d712e4823905c6679345ac') in 3.4399629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2378

========================================================================
Received Import Request.
  Time since last request: 1.600018 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab
  artifactKey: Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab using Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f985ae340b700d63285033b1b7758e8') in 0.7788786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2378

========================================================================
Received Import Request.
  Time since last request: 16.413732 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab
  artifactKey: Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/House 2.0.prefab using Guid(d28cab99e0bda9e4b8bf5c40780fb204) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd3d3d4014c7308e91d0114452e4b3d38') in 0.6166721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2378

========================================================================
Received Import Request.
  Time since last request: 113.500985 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/Models
  artifactKey: Guid(27fa78e4279224348bf2ca6745ceb682) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/Models using Guid(27fa78e4279224348bf2ca6745ceb682) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c2082bfb55b75c930f0e49c3f735eee2') in 0.0009308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.028164 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/balcony window.FBX
  artifactKey: Guid(50677a75f99b1e840af5022a95755761) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/balcony window.FBX using Guid(50677a75f99b1e840af5022a95755761) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '893399b4e90cff51e8a8f8db75ae33c4') in 0.1803732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/Door 1.FBX
  artifactKey: Guid(52fb20b76b26d12478d75abaacb54a94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/Door 1.FBX using Guid(52fb20b76b26d12478d75abaacb54a94) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98d957f1c381011873436a57f2539111') in 0.0434148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/oqra.FBX
  artifactKey: Guid(625434f789e9e554f94f75d695c3e5c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/oqra.FBX using Guid(625434f789e9e554f94f75d695c3e5c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '27ba5da6030f2b5cae0a637b49bf642e') in 0.0371186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/room window.FBX
  artifactKey: Guid(84b85f3d503522449aa2da96e52760eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/House2/Models/room window.FBX using Guid(84b85f3d503522449aa2da96e52760eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aed3b5cebdc454174821428a574284c2') in 0.0577904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 5.062099 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Resources
  artifactKey: Guid(db7de9f4034625349aa0606ba2d7fd87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Resources using Guid(db7de9f4034625349aa0606ba2d7fd87) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a0cda335a938a15427fe90a83133e33') in 0.0009561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Resources/VuforiaConfiguration.asset
  artifactKey: Guid(d333eee7031810f40a81c391e9284ee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Resources/VuforiaConfiguration.asset using Guid(d333eee7031810f40a81c391e9284ee7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '861664b025d47999eaaaf5436f802c54') in 0.0018114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 3.605238 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/StreamingAssets
  artifactKey: Guid(252b894a629c796489bd18cf860a822a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/StreamingAssets using Guid(252b894a629c796489bd18cf860a822a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8ec03682fb6abb89c1d7bee1bdd602af') in 0.0005581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.241750 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/StreamingAssets/Vuforia
  artifactKey: Guid(22a9927f1defa124a8cbe14fe4376831) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/StreamingAssets/Vuforia using Guid(22a9927f1defa124a8cbe14fe4376831) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '473dc3fd172b9f92bf6b92a3fc38eff8') in 0.0005186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 10.138164 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Vuforia/Editor/Scripts/VuforiaEditorScripts.asmdef
  artifactKey: Guid(bc72f8689eefa484699b4ed79b36553d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Vuforia/Editor/Scripts/VuforiaEditorScripts.asmdef using Guid(bc72f8689eefa484699b4ed79b36553d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '968f76d92fb37780a87e957697fcf248') in 0.0007866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.912966 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Scripts
  artifactKey: Guid(fd0b81281348b854cb3036e9eab89172) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Scripts using Guid(fd0b81281348b854cb3036e9eab89172) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f829fd0ba7fcb3bab082b061e70be31') in 0.0006867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.420888 seconds.
  path: Assets/TextMesh Pro/Resources/Sprite Assets/InlineIcons.png
  artifactKey: Guid(215687feeab0b7445b4a1cd86c82c2e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Sprite Assets/InlineIcons.png using Guid(215687feeab0b7445b4a1cd86c82c2e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '839ed940fc7ae6985cba981f4403c5b2') in 0.9653369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 207

========================================================================
Received Import Request.
  Time since last request: 1.547253 seconds.
  path: Packages/com.unity.shadergraph/Editor/Generation/Templates/BuildSurfaceDescriptionInputs.template.hlsl
  artifactKey: Guid(d3b47ff860a51904395688d633cb1018) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.shadergraph/Editor/Generation/Templates/BuildSurfaceDescriptionInputs.template.hlsl using Guid(d3b47ff860a51904395688d633cb1018) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be718204cf9d20410738aa4042d20227') in 0.0011243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Packages/com.unity.render-pipelines.core/Editor/Icons/RenderGraphViewer/<EMAIL>
  artifactKey: Guid(5e032f76278407d46a6a3dc6ff0bdc36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Packages/com.unity.render-pipelines.core/Editor/Icons/RenderGraphViewer/<EMAIL> using Guid(5e032f76278407d46a6a3dc6ff0bdc36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '283a702e7afefe723c53875915fa7b9e') in 0.0200454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 7.216562 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Camera2DMoveZoom.unity
  artifactKey: Guid(84b65774cb71fc543a5cba771cda3f7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Camera2DMoveZoom.unity using Guid(84b65774cb71fc543a5cba771cda3f7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2808546da74210f35140b395623db752') in 0.0005924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.696187 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Camera3DMoveZoom.unity
  artifactKey: Guid(4efc2379fae650d43b7a9c114f6e5772) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Camera3DMoveZoom.unity using Guid(4efc2379fae650d43b7a9c114f6e5772) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f3e297e4277f51512a53ea7e666d86d0') in 0.0006056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.021036 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/DoubleTapToSpawn.unity
  artifactKey: Guid(3ba993e45bd8bc540a58ab0bf1a234f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/DoubleTapToSpawn.unity using Guid(3ba993e45bd8bc540a58ab0bf1a234f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f25baa4d91a5f1a57c78911b37648616') in 0.0006154 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.897323 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/PressUITransform.unity
  artifactKey: Guid(d3900526662157a4aafcd4c727382385) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/PressUITransform.unity using Guid(d3900526662157a4aafcd4c727382385) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84b4d146195313fe4176647a8c89f26f') in 0.0006171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 38.848049 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/TwistRotateCustomAxis.unity
  artifactKey: Guid(1e746ea22b442744c8a81882c3144327) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/TwistRotateCustomAxis.unity using Guid(1e746ea22b442744c8a81882c3144327) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb9d840627482b7266aa78a5cbdc6b0d') in 0.0007827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 6.188764 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/TransformUI.unity
  artifactKey: Guid(6560ec49f82d0dd45bf84bd4db7bf43c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/TransformUI.unity using Guid(6560ec49f82d0dd45bf84bd4db7bf43c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '743d4ce32e80635c18ca5aded7f64e71') in 0.0006961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.374151 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Transform3DRelative.unity
  artifactKey: Guid(f4503f8040e549f489cd402bce299069) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Transform3DRelative.unity using Guid(f4503f8040e549f489cd402bce299069) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e066db6e90daf93e52effc6dff097e39') in 0.0004927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 10.218860 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Fonts
  artifactKey: Guid(4e21810f892d29041b7958e7f8e21f69) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Fonts using Guid(4e21810f892d29041b7958e7f8e21f69) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f35344005a03ded1344e28816140c71') in 0.0005772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.010971 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Fonts/BalooBhaina-Regular.ttf
  artifactKey: Guid(cee533bdaf5c01a4091b972ae1bbb654) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Fonts/BalooBhaina-Regular.ttf using Guid(cee533bdaf5c01a4091b972ae1bbb654) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '493bfc4e850c2ce8a3c0b7b2179d077c') in 0.0583167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Import Request.
  Time since last request: 4.285222 seconds.
  path: Assets/AR-House_Arcticture-unity-master/obj
  artifactKey: Guid(1fd7f9426b337e143bd8e7decbaffbad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/obj using Guid(1fd7f9426b337e143bd8e7decbaffbad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dcfc456a36b5e01c0a491c494ee9c172') in 0.0008923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.007081 seconds.
  path: Assets/AR-House_Arcticture-unity-master/obj/Debug
  artifactKey: Guid(1e14cac854a82224487e902bef7eafee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/obj/Debug using Guid(1e14cac854a82224487e902bef7eafee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '948518e92681dbb88f9cecb141039483') in 0.0005622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 23.203837 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Editor
  artifactKey: Guid(9640df89dcbe7f2419b9889742d579c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Editor using Guid(9640df89dcbe7f2419b9889742d579c4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7df198eb67b321b8a177c7e51de41294') in 0.0005981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.711750 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia
  artifactKey: Guid(b859b33624b03a8439ff43634b0e43f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia using Guid(b859b33624b03a8439ff43634b0e43f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '85290b5bb8952620da0052b7ca909720') in 0.0007681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.713822 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia/ImageTargetTextures
  artifactKey: Guid(d71c31c939417774190596839276a892) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia/ImageTargetTextures using Guid(d71c31c939417774190596839276a892) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c529a2b3c4daa1254b2c6f7ec48bae90') in 0.0006321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.699507 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia/ImageTargetTextures/AR_PROJECT_DB
  artifactKey: Guid(23ee2f37f859e0a4e93ab42ffda99bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia/ImageTargetTextures/AR_PROJECT_DB using Guid(23ee2f37f859e0a4e93ab42ffda99bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c8a5421d98b6af4a92e8f2f1f14d00e1') in 0.0006269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.025498 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia/ImageTargetTextures/AR_PROJECT_DB/IR_AR_scaled.jpg
  artifactKey: Guid(72bf756f9da34cc5bb63cd94d5c17b14) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/Editor/Vuforia/ImageTargetTextures/AR_PROJECT_DB/IR_AR_scaled.jpg using Guid(72bf756f9da34cc5bb63cd94d5c17b14) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00213260ad1ca876481aeea64558f3a6') in 0.0404019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 5.081732 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch
  artifactKey: Guid(943e336d7f4341245bc1333d3b410301) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch using Guid(943e336d7f4341245bc1333d3b410301) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '10f12a23a064609f30faf3718c8c67c8') in 0.0005807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.470362 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples
  artifactKey: Guid(4e3e60a20e28b03468a9907403888a53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples using Guid(4e3e60a20e28b03468a9907403888a53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1dbf2e6e1d41e218eed8fd02c646d518') in 0.0006402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.599808 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs
  artifactKey: Guid(decb7511b6092ef478409e3497030407) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs using Guid(decb7511b6092ef478409e3497030407) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '911e0ac90d99a646b824502f8b4ae360') in 0.000662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.013070 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs/Canvas.prefab
  artifactKey: Guid(e2a63f1f28575a549abdfbc4eb9b28d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs/Canvas.prefab using Guid(e2a63f1f28575a549abdfbc4eb9b28d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e729270368e6bfe84989d1780e5a37b0') in 0.0126467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 26

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs/Throwing Cube.prefab
  artifactKey: Guid(48ddf42ba625089439230f77ee081bff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs/Throwing Cube.prefab using Guid(48ddf42ba625089439230f77ee081bff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '55a6f6cb855cd9a9b508f25d72797047') in 0.0218214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs/Trail.prefab
  artifactKey: Guid(03f232e6ff0b4ff4da96e54c0e8a3371) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AR-House_Arcticture-unity-master/Assets/LeanTouch/Examples/Prefabs/Trail.prefab using Guid(03f232e6ff0b4ff4da96e54c0e8a3371) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6725eba79e186b0472d67e1b53502819') in 0.0471073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 63.61 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5853 unused Assets / (6.6 MB). Loaded Objects now: 7127.
Memory consumption went from 178.0 MB to 171.5 MB.
Total: 89.602400 ms (FindLiveObjects: 3.768100 ms CreateObjectMapping: 1.433900 ms MarkObjects: 75.685800 ms  DeleteObjects: 8.713000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.680 seconds
Refreshing native plugins compatible for Editor in 25.69 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.610 seconds
Domain Reload Profiling: 4284ms
	BeginReloadAssembly (513ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (139ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (995ms)
		LoadAssemblies (764ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (445ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (390ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (2611ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2157ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (388ms)
			ProcessInitializeOnLoadAttributes (1348ms)
			ProcessInitializeOnLoadMethodAttributes (358ms)
			AfterProcessingInitializeOnLoad (18ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 28.24 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 65 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5917 unused Assets / (7.0 MB). Loaded Objects now: 7090.
Memory consumption went from 227.7 MB to 220.7 MB.
Total: 25.122500 ms (FindLiveObjects: 1.186600 ms CreateObjectMapping: 1.573900 ms MarkObjects: 13.880200 ms  DeleteObjects: 8.480300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 6.76 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5856 unused Assets / (8.0 MB). Loaded Objects now: 7087.
Memory consumption went from 203.8 MB to 195.8 MB.
Total: 10.904700 ms (FindLiveObjects: 0.641300 ms CreateObjectMapping: 0.316400 ms MarkObjects: 6.177100 ms  DeleteObjects: 3.768600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 152.335615 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main
  artifactKey: Guid(b8210ab03e18e3e4f8c2fa911c085c9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main using Guid(b8210ab03e18e3e4f8c2fa911c085c9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f919740a58e830840a0da16426a0c3a') in 0.0028218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000966 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/final.gif
  artifactKey: Guid(df1b337641c4f4f48afb62a7a4a72e95) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/final.gif using Guid(df1b337641c4f4f48afb62a7a4a72e95) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '012f5933922aa69379ebec0c087e348e') in 0.0924171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.882587 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final
  artifactKey: Guid(648cf7d75d0673a49a4d43115dd6ef7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final using Guid(648cf7d75d0673a49a4d43115dd6ef7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c8540a7f8317555f47ed24f4639ae505') in 0.0006535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.588735 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets
  artifactKey: Guid(bfa5aab2977dc344aaa65e18778f1a3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets using Guid(bfa5aab2977dc344aaa65e18778f1a3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5c971c64ca0a65faf45f9182144c285d') in 0.0009382 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.670399 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit
  artifactKey: Guid(e309d610cb9154815b017a2db2bd7709) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit using Guid(e309d610cb9154815b017a2db2bd7709) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c59ffbc01451b7c28f9c8f7250e5c817') in 0.0005699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.023856 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/Default UITK Text Settings.asset
  artifactKey: Guid(fb48f3b3eecb047e88a6667306380d19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/Default UITK Text Settings.asset using Guid(fb48f3b3eecb047e88a6667306380d19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '032d770709cae456db3ff75851ae59d8') in 0.0014126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.129354 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/UnityThemes
  artifactKey: Guid(94d1130bfded57049b952a2e8e077aea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/UnityThemes using Guid(94d1130bfded57049b952a2e8e077aea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b961936090c048032953399aba2a50d') in 0.0005408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.052823 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/UnityThemes/UnityDefaultRuntimeTheme.tss
  artifactKey: Guid(56ace22954d8c084a9420b4ce8346a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/UnityThemes/UnityDefaultRuntimeTheme.tss using Guid(56ace22954d8c084a9420b4ce8346a7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28b4034d4fa63ef4f775495e65b5ac23') in 0.4361447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 2.747462 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/Resources
  artifactKey: Guid(441a96e1f31b84778bb5617592a4bb72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/Resources using Guid(441a96e1f31b84778bb5617592a4bb72) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'beb437352f6c7795e102602bb6d677f4') in 0.0005172 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 12.873193 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Assets/WUG/Scenes
  artifactKey: Guid(3737e2bec5c80414283431b0093470d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Assets/WUG/Scenes using Guid(3737e2bec5c80414283431b0093470d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a3c6f7bcbe8b0c23e32803daecf43c6c') in 0.0005081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.630206 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Assets/WUG/Scenes/Demo.unity
  artifactKey: Guid(b0af07938f7201541bf01b7de7a1368d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Assets/WUG/Scenes/Demo.unity using Guid(b0af07938f7201541bf01b7de7a1368d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83c1e553d1fca80689bdeb918355c5c8') in 0.0005744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 27.091794 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Assets/WUG/Scripts/GameController.cs
  artifactKey: Guid(1407edcc3e886cb40bd0e96d94010116) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Assets/WUG/Scripts/GameController.cs using Guid(1407edcc3e886cb40bd0e96d94010116) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7713943d3a2e5cb811860f6b0c2c557') in 0.0006802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 72.757508 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter
  artifactKey: Guid(85ca856ffef4d1e438d2b03739e0c6d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter using Guid(85ca856ffef4d1e438d2b03739e0c6d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '263891aa7df9dac2e4892d37630ba69a') in 0.0007058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.669604 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings
  artifactKey: Guid(dabf1f26c7aa829489aa31d0144e588a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings using Guid(dabf1f26c7aa829489aa31d0144e588a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9a7837e73900054ac7936c112a0aa5a8') in 0.0009676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.007533 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/AudioManager.asset
  artifactKey: Guid(431aca6441b5a6d4aa3f502fb0ce8a5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/AudioManager.asset using Guid(431aca6441b5a6d4aa3f502fb0ce8a5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f967a6b53b56240886e6d6fff4e34eb4') in 0.0008221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/EditorBuildSettings.asset
  artifactKey: Guid(708d1858fc67036449a89fc1bc3d3238) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/EditorBuildSettings.asset using Guid(708d1858fc67036449a89fc1bc3d3238) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '02290bad25b1c4df7c8efc5a5c25fc5e') in 0.0009214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/QualitySettings.asset
  artifactKey: Guid(9228334223e112a4eac2feff0b7f01cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/QualitySettings.asset using Guid(9228334223e112a4eac2feff0b7f01cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f92609f1a2ef31e378ec6d1cbe3ff357') in 0.0011656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000450 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/VFXManager.asset
  artifactKey: Guid(3d34ce0d0cbda484bb2186c4f89d7a0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/VFXManager.asset using Guid(3d34ce0d0cbda484bb2186c4f89d7a0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9280e458e512be59613faac72999bc2') in 0.000931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/Physics2DSettings.asset
  artifactKey: Guid(46275af678b74434dbcf3d3ffda7966f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/Physics2DSettings.asset using Guid(46275af678b74434dbcf3d3ffda7966f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b7b1cbf7ea58a462583a0160ace0155') in 0.0012157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/NavMeshAreas.asset
  artifactKey: Guid(4e7a005ec219f4440b47a8867e5b4e3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/NavMeshAreas.asset using Guid(4e7a005ec219f4440b47a8867e5b4e3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '775e8e8065bcc82d15ce2da17941b676') in 0.0008734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/GraphicsSettings.asset
  artifactKey: Guid(aa05f21c95b11d84b941648b822bedc9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/GraphicsSettings.asset using Guid(aa05f21c95b11d84b941648b822bedc9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '42ca2e4cd1149619ddc44aa1cbbbc51f') in 0.0009942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/DynamicsManager.asset
  artifactKey: Guid(d934a7f408ed7124a814a08519f0ed2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/DynamicsManager.asset using Guid(d934a7f408ed7124a814a08519f0ed2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '969f7275b571191c98183be260d0bf99') in 0.0011133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/TimeManager.asset
  artifactKey: Guid(170141f75fe0b5d469f7261d096400a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/TimeManager.asset using Guid(170141f75fe0b5d469f7261d096400a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '168a2bc9886344150f890149199f640b') in 0.0009468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 4.055479 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Packages
  artifactKey: Guid(782f708599ddb574c9d7854cb17dd675) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/Packages using Guid(782f708599ddb574c9d7854cb17dd675) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5ee3ad5d66c7d628d40d430f7c9ede69') in 0.0005177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.395511 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/UserSettings
  artifactKey: Guid(9f5bde7cf64eeeb4da194ba4e15477a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/UserSettings using Guid(9f5bde7cf64eeeb4da194ba4e15477a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6d9fd591b08cca38048dea070698e23') in 0.0005307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.002757 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/UserSettings/EditorUserSettings.asset
  artifactKey: Guid(c2e23b9c30b024c4d9c1c1a29e79fb45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/UserSettings/EditorUserSettings.asset using Guid(c2e23b9c30b024c4d9c1c1a29e79fb45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1bfeb9329c5760150700c656dde74bdb') in 0.0008588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.655498 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/ProjectSettings/ClusterInputManager.asset
  artifactKey: Guid(5f6b40fc511ac48428e46ba0ffd9ab66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/ProjectSettings/ClusterInputManager.asset using Guid(5f6b40fc511ac48428e46ba0ffd9ab66) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ce54bce0c4de1020c571fbc6692780f') in 0.0006964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/UserSettings/EditorUserSettings.asset
  artifactKey: Guid(b7b679d7cbe32dc429c6c21f3c94e631) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/UserSettings/EditorUserSettings.asset using Guid(b7b679d7cbe32dc429c6c21f3c94e631) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0722e8ed44b2ac84047eab7a5f4c087a') in 0.001029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

