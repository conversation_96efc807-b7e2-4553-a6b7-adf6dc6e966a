﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_6;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;UNITY_POST_PROCESSING_STACK_V2;USE_STICK_CONTROL_THUMBSTICKS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.6f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataSelectableBool.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataSlider.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\Inputs\DemoAdjustMainSceneForVive.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Helpers\HoverIndicatorOverrider.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastMirrorSwitcher.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverFillButtonRectUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Cursors\HoverRendererIdle.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\IGameObjectProvider.cs" />
    <Compile Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\Assets\WUG\Scripts\InventoryUIController.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\HoverItem.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Utils\RendererUtil.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\RandomUtil.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\PermissionsManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Helpers\HoverRendererProximityDebugger.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\VideoPlayerRenderTexture.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoLightPosLabel.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoHandlers.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Sliders\HoverRendererSliderSegments.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Cursors\HoverFillIdle.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataRadio.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoCubeHold.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\Rotator.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\XRKnob.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\DemoAdjustCursorsForHovercastOculusTouch.cs" />
    <Compile Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\Assets\WUG\Scripts\InventorySlot.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelAlphaUpdater.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\BooleanToggleVisualsController.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Cursors\HoverRaycastLine.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\SliderFillType.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\RendererModules\Alpha\Scripts\HoverAlphaIdleRendererUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Key\Scripts\HoverkeyTextInput.cs" />
    <Compile Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-starter\Assets\WUG\Scripts\GameController.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\RayAttachModifier.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Rect\HoverLayoutRectRow.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoEnvironment.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Helpers\ShowViaHoverItemBoolValue.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Helpers\ColorViaHoverIndicator.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Key\Scripts\HoverkeyBuilderData.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\TriggerButtonAttribute.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\CircularMotion.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoEnvironment.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelHighlightPreventer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\HoverLayoutArcGroupChild.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\ICursorData.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Key\Scripts\HoverkeyInterface.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\Inputs\DemoAdjustMainSceneForNone.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverMeshRect.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoLetter.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\XRPokeFollowAffordanceFill.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\LeapMotionOld\Scripts\HoverInputLeapMotionOld.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemsStickyManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Buttons\HoverRendererButton.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelRowSwitchingInfo.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\ISettingsControllerMap.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Rect\ILayoutableRect.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\IItemData.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\HoverItemBuilder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\HoverRenderer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemsManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelInterface.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\DemoAdjustInputSceneCamera.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataSticky.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Key\Scripts\HoverkeyBuilder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\RendererModules\Opaque\Scripts\HoverOpaqueMeshUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\Vive\Scripts\ViveCursor.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\HoverInputMissing.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\HoverMesh.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\DisplayUtil.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastInterface.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\GifAnim\Scripts\GifAnimDarkTheme.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Sliders\HoverFillSliderUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\RendererModules\Alpha\Scripts\HoverAlphaRendererUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverCursorData.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverCursorIdleState.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\RendererModules\Alpha\Scripts\HoverAlphaMeshUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\FollowTransform.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\ICursorDataForInput.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelRowTransitioner.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataSelectableT.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\IInteractionSettings.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\TreeUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelActiveDirection.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\RendererModules\Alpha\Scripts\HoverAlphaFillTabUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Key\Scripts\HoverkeyBuilderKeyInfo.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\CanvasElements\HoverIcon.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataCheckbox.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\CursorUtil.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataSelectableFloat.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\BezierCurve.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastRowSwitchingInfo.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverCursorDataProvider.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\LaunchProjectile.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastBuilder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemsRaycastManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverRendererSliderRectUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Helpers\ScaleViaHoverIndicator.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataText.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\DisableWhenControlledAttribute.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\CanvasElements\HoverCanvas.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\HoverSceneAdjust.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoBoxes.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\LookAt.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemSelectionState.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\HandSubsystemManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataSelector.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\ITreeUpdateable.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\SettingsControllerMap.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverFillSliderRectUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\HoverFill.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Rect\HoverLayoutRectGroup.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverMeshArc.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoHovercastListener.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\OculusTouch\Scripts\OculusTouchCursor.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastRowTransitioner.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastOpenIcons.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Sliders\HoverRendererSliderUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\ItemEvents.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastOpenTransitioner.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemHighlightState.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\Vive\Scripts\HoverInputVive.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Helpers\HoverChildItemsEnabler.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\CursorType.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\HoverItemRendererUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\Inputs\DemoAdjustMainSceneForOculusTouch.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverMeshRectHollow.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataSticky.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoRing.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\DestroyObject.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\VideoTimeScrubControl.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverCursorFollower.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Rect\HoverLayoutRectRelativeSizer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastRowTitle.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoHovercastCustomizer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\Follow\Scripts\HoverInputFollow.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\RendererModules\Alpha\Scripts\HoverAlphaFillUpdater.cs" />
    <Compile Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\Assets\WUG\Scripts\GameController.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverRendererIdleArcUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataCheckbox.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\KeyboardPixels\Scripts\DemoTextPixels.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\StickySelectionInfo.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\MeshUtil.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Cursors\HoverFillCursor.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelRowSizer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\HoverItemData.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\HoverLayoutArcGroup.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\Inputs\DemoAdjustMainSceneForLeap.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\GifAnim\Scripts\GifAnimCubes.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Rect\HoverLayoutRectPaddingSettings.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataSelector.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Utils\SliderUtil.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataSelectable.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataText.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\RaycastResult.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\FollowCursor.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\LeapMotion\Scripts\HoverInputLeapMotion.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\ILayoutableArc.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\DemoAdjustCursorsForHovercastVive.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoColorHueRendererUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\CanvasElements\HoverLabel.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoAnim.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastAlphaUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\LayoutUtil.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastActiveDirection.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\Callout.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\ISettingsController.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverFillButtonArcUpdater.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\CalloutGazeController.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoHandlers.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Panel\Scripts\HoverpanelBuilder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\CursorCapabilityType.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverInteractionSettings.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\HoverLayoutArcRow.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\CanvasElements\HoverCanvasDataUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\HoverLayoutArcStack.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\HoverLayoutArcPaddingSettings.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemsHighlightManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Managers\HoverItemsRadioManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Helpers\HoverItemEventReactor.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverRendererSliderSegmentsRect.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\Common\DemoAdjustCursorsForHovercastDefault.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataSlider.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\AnchorType.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\IItemDataSelectable.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Buttons\HoverFillButton.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastBackCursorTrigger.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoCube.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverShapeRect.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\HoverIndicator.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\HoverDemos\CastCubes\Scripts\DemoMotion.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InputModules\OculusTouch\Scripts\HoverInputOculusTouch.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Cast\Scripts\HovercastHighlightPreventer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Helpers\HoverChildItemsFinder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\IProximityProvider.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Arc\HoverLayoutArcRelativeSizer.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\CanvasElements\HoverCanvasSizeUpdater.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\StepManager.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverFillSliderArcUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverRendererSliderSegmentsArc.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverRendererSliderArcUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\SelectorActionType.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\ICursorIdle.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverIdleRendererUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Sliders\HoverRendererSlider.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverCursorRendererUpdater.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\HoverSceneLoader.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Arc\HoverShapeArc.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Items\Types\HoverItemDataRadio.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\HoverShape.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Cursors\HoverRendererCursor.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Cursors\HoverCursorRenderersBuilder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Shapes\Rect\HoverMeshRectHollowTab.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Renderers\Items\Sliders\HoverFillSlider.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Layouts\Rect\HoverLayoutRectGroupChild.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\UnityUtil.cs" />
    <Compile Include="Assets\VRTemplateAssets\Scripts\AnchorVisuals.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Scripts\Utils\MeshBuilder.cs" />
    <Compile Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\InterfaceModules\Key\Scripts\HoverkeyItemLabels.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Resources\Shaders\VertColorTexTwoSidedAlpha.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets\VRTemplateAssets\Shaders\FauxBlurURP.shader" />
    <None Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-starter\ProjectSettings\ProjectVersion.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\Core\Resources\Shaders\VertColorTexTwoSidedOpaque.shader" />
    <None Include="Assets\Texture\sun.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\Hover-UI-Kit-master\Unity\ProjectSettings\ProjectVersion.txt" />
    <None Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\Assets\WUG\UI\Inventory.uss" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\Hover-UI-Kit-master\Unity\Assets\Hover\NamespaceDocs.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\SDFFunctions.hlsl" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\VRTemplateAssets\Shaders\Grid.shader" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\Assets\WUG\UI\Inventory.uxml" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\Assets\UI Toolkit\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\VRTemplateAssets\Shaders\TexturedFresnelStandard.shader" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\VRTemplateAssets\Shaders\TexturedStableFresnelCommon.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\create-a-runtime-inventory-with-UI-Toolkit-main\Runtime-Inventory-final\ProjectSettings\ProjectVersion.txt" />
    <None Include="Assets\VRTemplateAssets\Materials\Skybox\Horizontal Skybox.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Editor.csproj" />
    <ProjectReference Include="Unity.InputSystem.ForUI.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="Unity.Searcher.Editor.csproj" />
    <ProjectReference Include="Unity.Rider.Editor.csproj" />
    <ProjectReference Include="Unity.XR.CoreUtils.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Features.ConformanceAutomation.csproj" />
    <ProjectReference Include="Unity.Tutorials.Core.Editor.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Features.OculusQuestSupport.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.Hands.csproj" />
    <ProjectReference Include="Unity.InternalAPIEditorBridge.007.csproj" />
    <ProjectReference Include="Unity.RenderPipeline.Universal.ShaderLibrary.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Universal.Shaders.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.Shared.csproj" />
    <ProjectReference Include="Unity.Timeline.Editor.csproj" />
    <ProjectReference Include="Unity.Timeline.csproj" />
    <ProjectReference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.Shared.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.ShaderLibrary.csproj" />
    <ProjectReference Include="Unity.TestTools.CodeCoverage.Editor.csproj" />
    <ProjectReference Include="Unity.PlasticSCM.Editor.csproj" />
    <ProjectReference Include="Unity.Multiplayer.Center.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj" />
    <ProjectReference Include="Unity.ShaderGraph.Editor.csproj" />
    <ProjectReference Include="Unity.Mathematics.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Hands.Samples.VisualizerSample.csproj" />
    <ProjectReference Include="Unity.Performance.Profile-Analyzer.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.GPUDriven.Runtime.csproj" />
    <ProjectReference Include="UnityEditor.XR.LegacyInputHelpers.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj" />
    <ProjectReference Include="Unity.Collections.csproj" />
    <ProjectReference Include="PPv2URPConverters.csproj" />
    <ProjectReference Include="Unity.Rendering.LightTransport.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Management.Editor.csproj" />
    <ProjectReference Include="UnityEngine.XR.LegacyInputHelpers.csproj" />
    <ProjectReference Include="Unity.XR.Hands.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Universal.Config.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Universal.Editor.csproj" />
    <ProjectReference Include="Unity.InternalAPIEngineBridge.007.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj" />
    <ProjectReference Include="Unity.Collections.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj" />
    <ProjectReference Include="UnityEngine.SpatialTracking.csproj" />
    <ProjectReference Include="Unity.Multiplayer.Center.Common.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Hands.csproj" />
    <ProjectReference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj" />
    <ProjectReference Include="Unity.XR.Management.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.csproj" />
    <ProjectReference Include="Unity.EditorCoroutines.Editor.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Editor.csproj" />
    <ProjectReference Include="Unity.Mathematics.csproj" />
    <ProjectReference Include="Assembly-GraphicMaterialOverride.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Features.MetaQuestSupport.csproj" />
    <ProjectReference Include="Unity.Settings.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Universal.Runtime.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.Editor.csproj" />
    <ProjectReference Include="Unity.Burst.Editor.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.csproj" />
    <ProjectReference Include="Unity.Burst.csproj" />
    <ProjectReference Include="Unity.VisualStudio.Editor.csproj" />
    <ProjectReference Include="Unity.Tutorials.Core.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj" />
    <ProjectReference Include="Assembly-AnimateGraphicMaterials-Editor.csproj" />
    <ProjectReference Include="Unity.XR.CoreUtils.Editor.csproj" />
    <ProjectReference Include="Unity.Rendering.LightTransport.Runtime.csproj" />
    <ProjectReference Include="UnityEditor.SpatialTracking.csproj" />
    <ProjectReference Include="Unity.InputSystem.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Universal.2D.Runtime.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
