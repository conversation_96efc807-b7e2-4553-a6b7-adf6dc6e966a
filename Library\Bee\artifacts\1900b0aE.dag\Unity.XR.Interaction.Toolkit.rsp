-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.ref.dll"
-define:UNITY_6000_1_6
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:USE_INPUT_SYSTEM_POSE_CONTROL
-define:UNITY_POST_PROCESSING_STACK_V2
-define:USE_STICK_CONTROL_THUMBSTICKS
-define:ANIMATION_MODULE_PRESENT
-define:PHYSICS2D_MODULE_PRESENT
-define:BURST_PRESENT
-define:XR_HANDS_1_1_OR_NEWER
-define:XR_HANDS_1_3_OR_NEWER
-define:XR_MANAGEMENT_4_0_OR_NEWER
-define:OPENXR_1_6_OR_NEWER
-define:OPENXR_1_7_OR_NEWER
-define:OPENXR_1_10_OR_NEWER
-define:XR_LEGACY_INPUT_HELPERS_2_1_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.CoreUtils.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Hands.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Management.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.OpenXR.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.SpatialTracking.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.XR.LegacyInputHelpers.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Jobs/ColorTweenJob.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Jobs/FloatTweenJob.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Jobs/ITweenJob.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Jobs/TweenJobData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Audio/AudioAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/BaseAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/BaseAsyncAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/BaseSynchronousAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/IAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/IAsyncAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/ISynchronousAffordanceStateReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/ColorAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/FloatAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/QuaternionAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/QuaternionEulerAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/Vector2AffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/Vector3AffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Primitives/Vector4AffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/BlendShapeAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/ColorGradientLineRendererAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/ColorMaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/FloatMaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/Vector2MaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/Vector3MaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Rendering/Vector4MaterialPropertyAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/Transformation/UniformTransformScaleAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Receiver/UI/ImageColorAffordanceReceiver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Rendering/MaterialHelperBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Rendering/MaterialInstanceHelper.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Rendering/MaterialPropertyBlockHelper.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/State/Data/AffordanceStateData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/State/Data/AffordanceStateShortcuts.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/State/Provider/BaseAffordanceStateProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/State/Provider/XRInteractableAffordanceStateProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/State/Provider/XRInteractorAffordanceStateProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/Audio/AudioAffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/BaseAffordanceTheme.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/Primitives/ColorAffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/Primitives/FloatAffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/Primitives/Vector2AffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/Primitives/Vector3AffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AffordanceSystem/Theme/Primitives/Vector4AffordanceThemeDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/DragGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/DragGesture.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/DragGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/Gesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/Gesture.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/GestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/GestureRecognizer.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/GestureTouchesUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/PinchGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/PinchGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TapGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TapGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TwistGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TwistGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TwoFingerDragGesture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TwoFingerDragGesture.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Gestures/TwoFingerDragGestureRecognizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/ScreenSpacePinchScaleInput.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/ScreenSpaceRayPoseDriver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/ScreenSpaceRotateInput.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/ScreenSpaceSelectInput.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/TouchscreenGestureInputController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/TouchscreenGestureInputControllerState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/TouchscreenGestureInputLayoutLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Inputs/TouchscreenGestureInputLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARAnnotationInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARBaseGestureInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARPlacementInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARRotationInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARScaleInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARSelectionInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/ARTranslationInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactables/GestureTransformationUtility.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactors/ARGestureInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AR/Interactors/IARInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Feedback/SimpleAudioFeedback.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Feedback/SimpleHapticFeedback.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/CardinalUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Composites/FallbackComposite.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/HapticControlActionManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/HapticImpulseCommandChannel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/HapticImpulseCommandChannelGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/HapticImpulsePlayer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/HapticImpulseSingleChannelGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/HapticsUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/IXRHapticImpulseChannel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/IXRHapticImpulseChannelGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/IXRHapticImpulseProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/OpenXR/OpenXRHapticImpulseChannel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/XRInputDeviceHapticImpulseChannel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/XRInputDeviceHapticImpulseChannelGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/XRInputDeviceHapticImpulseProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Haptics/XRInputHapticImpulseProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/InputActionManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/InputActionPropertyExtensions.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/InputActionUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Interactions/SectorInteraction.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/InputFeatureUsageString.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputButtonReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceBoolValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceButtonReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceFloatValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceInputTrackingStateValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceQuaternionValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceVector2ValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputDeviceVector3ValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputReaderUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Readers/XRInputValueReader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/Hands/HandExpressionCapture.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/Hands/HandExpressionName.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/Hands/XRDeviceSimulatorHandsProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/Hands/XRDeviceSimulatorHandsSubsystem.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/Hands/XRSimulatedHandState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/SimulatedDeviceLifecycleManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/SimulatedHandExpression.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/SimulatedHandExpressionManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/SimulatedInputLayoutLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRDeviceSimulator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRDeviceSimulator.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRDeviceSimulatorLoader.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRDeviceSimulatorSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRInteractionSimulator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRInteractionSimulatorLoader.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRSimulatedController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRSimulatedControllerState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRSimulatedHMD.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRSimulatedHMDState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/Simulation/XRSimulatorUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/XRHandSkeletonPokeDisplacer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/XRInputModalityManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/XRInputTrackingAggregator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Inputs/XRTransformStabilizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attachment/AttachPointVelocityTracker.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attachment/IAttachPointVelocityProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attachment/IAttachPointVelocityTracker.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attachment/IFarAttachProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attachment/IInteractionAttachController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attachment/InteractionAttachController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attributes/CanFocusMultipleAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attributes/CanSelectMultipleAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Attributes/XRTargetEvaluatorEnabledAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/ActionBasedController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/InteractionState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/InteractionState.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRBaseController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRControllerRecorder.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRControllerRecorder.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRControllerRecording.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRControllerRecording.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRControllerState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRControllerState.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Controllers/XRScreenSpaceController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Hover/IXRHoverFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Hover/TouchscreenHoverFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/InteractionStrength/IXRInteractionStrengthFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/IXRFilterList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/IMultiPokeStateDataProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/IPokeStateDataProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/IXRPokeFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/PokeStateData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/PokeThresholdData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/PokeThresholdDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/PokeThresholdDatumProperty.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/XRPokeFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Poke/XRPokeLogic.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Select/IXRSelectFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Evaluators/IXRTargetEvaluatorLinkable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Evaluators/XRAngleGazeEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Evaluators/XRDistanceEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Evaluators/XRLastSelectedEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Evaluators/XRTargetEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Filters/IXRTargetFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Filters/XRBaseTargetFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Filtering/Target/Filters/XRTargetFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Gaze/IXRAimAssist.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Gaze/IXROverridesGazeAutoSelect.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Gaze/XRGazeAssistance.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/DistanceInfo.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/IXRActivateInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/IXRFocusInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/IXRHoverInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/IXRInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/IXRInteractionStrengthInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/IXRSelectInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/Visuals/IXRInteractableCustomReticle.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/Visuals/XRTintInteractableVisual.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/XRBaseInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/XRBaseInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/XRGrabInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/XRGrabInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/XRInteractableSnapVolume.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactables/XRSimpleInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Casters/CurveInteractionCaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Casters/ICurveInteractionCaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Casters/IInteractionCaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Casters/InteractionCasterBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Casters/SphereInteractionCaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/InteractorHandedness.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRActivateInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRGroupMember.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRHoverInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRInteractionGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRInteractionOverrideGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRInteractionStrengthInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRRayProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRScaleValueProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRSelectInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/IXRTargetPriorityInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/NearFarInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Visuals/CurveVisualController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Visuals/ICurveInteractionDataProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Visuals/IXRCustomReticleProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Visuals/IXRReticleDirectionProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Visuals/XRInteractorLineVisual.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/Visuals/XRInteractorReticleVisual.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRBaseControllerInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRBaseInputInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRBaseInputInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRBaseInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRBaseInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRDirectInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRGazeInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRInteractionGroup.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRPokeInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRRayInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRRayInteractor.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Interactors/XRSocketInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Layers/InteractionLayerMask.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Layers/InteractionLayerSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/ARTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/IXRDropTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/IXRGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/XRBaseGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/XRDualGrabFreeTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/XRGeneralGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/XRLegacyGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/XRSingleGrabFreeTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/Transformers/XRSocketGrabTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/XRInteractionEvents.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/XRInteractionEvents.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/XRInteractionManager.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/XRInteractionManager.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Interaction/XRInteractionUpdateOrder.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/CharacterControllerBodyManipulator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Climbing/ClimbInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Climbing/ClimbProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Climbing/ClimbSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Climbing/ClimbSettingsDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Climbing/ClimbSettingsDatumProperty.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Climbing/ClimbTeleportInteractor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Comfort/TunnelingVignetteController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Gravity/GravityOverride.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Gravity/GravityProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Gravity/IGravityController.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/IConstrainedXRBodyManipulator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/IXRBodyPositionEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/IXRBodyTransformation.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Jump/JumpProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/ActionBasedContinuousMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/ActionBasedContinuousTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/ActionBasedSnapTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/CharacterControllerDriver.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/ContinuousMoveProviderBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/ContinuousTurnProviderBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/DeviceBasedContinuousMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/DeviceBasedContinuousTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/DeviceBasedSnapTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/LocomotionPhase.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/LocomotionSystem.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Legacy/SnapTurnProviderBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/LocomotionMediator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/LocomotionProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/LocomotionProvider.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/LocomotionState.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/ConstrainedMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/ConstrainedMoveProvider.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/ContinuousMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/ContinuousMoveProvider.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/GrabMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/GrabMoveProvider.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Movement/TwoHandedGrabMoveProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/ScriptableConstrainedBodyManipulator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/BaseTeleportationInteractable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/BaseTeleportationInteractable.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/FurthestTeleportationAnchorFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/GazeTeleportationAnchorFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/ITeleportationVolumeAnchorFilter.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportationAnchor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportationArea.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportationMultiAnchorVolume.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportationProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportingEventArgs.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportVolumeDestinationSettings.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportVolumeDestinationSettingsDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Teleportation/TeleportVolumeDestinationSettingsDatumProperty.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Turning/ContinuousTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/Turning/SnapTurnProvider.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/UnderCameraBodyPositionEvaluator.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/XRBodyTransformations.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/XRBodyTransformer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Locomotion/XRMovableBody.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/BodyUI/FollowPresetDatum.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/BodyUI/HandMenu.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/CanvasOptimizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/CanvasTracker.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/IUIModelUpdater.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/LazyFollow.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/NavigationModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/PointerModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/RegisteredUIInteractorCache.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/TouchModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/TrackedDeviceEventData.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/TrackedDeviceGraphicRaycaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/TrackedDeviceModel.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/TrackedDeviceModel.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/TrackedDevicePhysicsRaycaster.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/UIInputModule.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/UIInputModule.Events.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/XRUIInputModule.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/UI/XRUIInputModule.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/BurstGazeUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/BurstLerpUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/BurstMathUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/BurstPhysicsUtils.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Collections/CircularBuffer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Collections/NativeCurve.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/ComponentLocatorUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Curves/CurveUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/DisposableManagerSingleton.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/EditorComponentLocatorUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/ExposedRegistrationList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/GradientUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Interaction/XRFilterUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Interaction/XRInteractableUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Locomotion/LocomotionUtility.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Pooling/LinkedPool.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Pooling/PooledObject.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/ProjectPath.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/RegistrationList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/RequireInterfaceAttribute.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/ScriptableSingletonCache.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/SmallRegistrationList.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/TeleportationMonitor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/TriggerContactMonitor.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/Primitives/ColorTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/Primitives/FloatTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/Primitives/QuaternionTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/Primitives/Vector2TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/Primitives/Vector3TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/Primitives/Vector4TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/SmartTweenableVariables/SmartFollowQuaternionTweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/SmartTweenableVariables/SmartFollowVector3TweenableVariable.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/TweenableVariableAsyncBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/TweenableVariableBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/Tweenables/TweenableVariableSynchronousBase.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/UnityObjectReferenceCache.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/Utilities/XRDebugLineVisualizer.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/XR/GizmoHelpers.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/XR/InputHelpers.deprecated.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/XR/SortingHelpers.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/XR/XRRig.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/XRHelpURLConstants.cs"
"Library/PackageCache/com.unity.xr.interaction.toolkit@9b07900cb163/Runtime/XRHelpURLConstants.deprecated.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Unity.XR.Interaction.Toolkit.UnityAdditionalFile.txt"