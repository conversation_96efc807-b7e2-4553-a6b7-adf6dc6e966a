<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="Inventory.uss" />
    <ui:VisualElement name="Container" class="container">
        <ui:VisualElement name="Inventory" class="inventory">
            <ui:Label text="Inventory" display-tooltip-when-elided="true" name="Header" class="header" />
            <ui:VisualElement name="SlotContainer" class="slotsContainer" style="align-items: center; justify-content: center; flex-direction: row; margin-top: 0; margin-left: 0; margin-right: 0; margin-bottom: 0; flex-shrink: 1;" />
        </ui:VisualElement>
        <ui:VisualElement name="GhostIcon" style="visibility: hidden; position: absolute; width: 128px; height: 128px; background-image: none;" />
    </ui:VisualElement>
</ui:UXML>
