﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Unity.RenderPipelines.Universal.Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_6;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;UNITY_POST_PROCESSING_STACK_V2;USE_STICK_CONTROL_THUMBSTICKS;ENABLE_XR_MODULE;XR_MANAGEMENT_4_0_1_OR_NEWER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.6f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\ModelPostProcessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Targets\UniversalSpriteCustomLitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalAdditionalLightDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\GUIState.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Converter\UpgradeURP2DAssetsContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\ISelector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.PhysicalCamera.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXURPLitMeshOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\ConverterItemInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\DisplacableRectHandles.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\ISnapping.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Converter\ParametricToFreeformLightUpgrader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\DepthOfFieldEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalSixWaySubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\DecalShaderGraphGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\IndexedSelection.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\SixWayGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineAsset\UniversalRenderPipelineAssetUI.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Shapes\Spline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\AssetCallbacks\CreateCanvasShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Settings\PropertyDrawers\URPShaderStrippingSettingsPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\BakedLitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Shadows\ShadowProvider\ShadowShape2DProvider_SpriteSkin_PropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ParticleGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineLightEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRendererDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\Snapping.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\SimpleLitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\ParticlesUnlitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderStripTool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\PostProcessDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineVolumeComponentEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShadingModels\LitDetailGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Renderer2DAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\ConversionIndexers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\RunItemContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\View\IDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\BuiltInToURPConverterContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\DecalProjectorEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\SerializableSelection.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\GenericControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\RenderPipelineConverterContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\SketchupMaterialDescriptionPostprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalLitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\MotionBlurEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineAsset\SerializedUniversalRenderPipelineAsset.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXAbstractParticleURPLitOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\PathComponentEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\CreateDecalShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\SortingLayerDropDown.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Renderer2DMenus.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\FBXMaterialDescriptionPreprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\UnlitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\PathEditorTool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\EditablePath.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\BuildProcessors\GraphicsSettingsStrippers\ScreenSpaceAmbientOcclusionStripper.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Targets\SpriteSubTargetUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\PathEditorToolExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\InitializeConverterContext.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShaderGraphLitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Shadows\ShadowCaster2DShapeTool.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\CompositeShadowCaster2DEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalSpeedTree8MaterialUpgrader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Converter\URP2DConverterUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\GUISystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\ControlPoint.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShadergraphSpriteGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\DefaultControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\ScriptableData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\ConverterItemDescriptor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\NewPostProcessTemplateDropdownItems.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineAsset\UniversalRenderPipelineAssetUI.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\UniversalStructFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\BuildProcessors\URPBuildDataValidator.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Lighting\UniversalRenderPipelineLightUI.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\ScreenSpaceLensFlareEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalDecalSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\ColorLookupEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Rendering.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\SliderAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\IEditablePath.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXURPLitPlanarPrimitiveOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\ISelectable.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\ChannelMixerEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShadingModels\BakedLitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Shapes\Polygon.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalUnlitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\ScreenSpaceShadowsEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\EditablePathUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\DefaultScene\UniversalProjectSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\LightBatchingDebugger\LightBatchingDebugger.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\FullScreenPassRendererFeatureEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\AssetCallbacks\CreateSixWayShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\IGUIState.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Lighting\UniversalRenderPipelineLightUI.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Targets\UniversalSpriteUnlitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\AssetCallbacks\CreateUnlitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Environment.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Light2DEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineSerializedCamera.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\AnimationClipConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Lighting\UniversalRenderPipelineLightUI.PresetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\TerrainLitShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Output.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalFullscreenSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\ScreenSpaceAmbientOcclusionEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\View\Drawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Converter\BuiltInToURP2DConverterContainer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\View\IEditablePathView.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\RenderObjectsPassFeatureEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\TrackballUIDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\CinemachineUniversalPixelPerfectEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ScriptableRendererDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Shadows\ShadowCaster2DEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineAssetEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Rendering.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Renderer2DDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\GenericScriptablePathInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UpgradeUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderBuildPreprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShadingModels\SimpleLitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\ScriptablePathInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ScriptableRendererFeatureProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\MaterialPostprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\ISelection.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\UniversalProperties.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\RenderPipelineConvertersEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\PhysicalMaterial3DsMaxPreprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\EditablePathExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\CreateDecalProjector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\AssetCallbacks\CreateLitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\HoveredControlAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXURPBinder.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\ShaderGraphMaterialsUpdater.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\LightExplorer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\BuildProcessors\URPBuildData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Shapes\ShapeExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Analytics\AssetReimporterAnalytic.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\BuildProcessors\URPProcessScene.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Lighting\UniversalRenderPipelineSerializedLight.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\DecalMeshBiasTypeEnum.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\ThreeDSMaterialDescriptionPostprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Settings\PropertyDrawers\URPDefaultVolumeProfileSettingsPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\UniversalStructs.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Analytics\PostProcessDataAnalytics.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\IEditablePathController.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\IUndoObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\GenericScriptablePath.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Environment.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\GenericDefaultControl.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\ColorCurvesEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderScriptableStripper.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\View\CreatePointAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXURPLitQuadStripOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\BuildProcessors\GraphicsSettingsStrippers\RendererStripper.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXShaderGraphGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\UniversalBlockFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\ParticlesSimpleLitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShaderGraphUnlitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\MultipleEditablePathController.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Targets\UniversalSpriteLitSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\FilmGrainEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXURPSubOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\GameObjectCreation.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalAdditionalCameraDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\DecalRendererFeatureEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\NewRendererFeatureDropdownItem.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\BezierUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Settings\PropertyDrawers\URPRenderGraphPropertyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AnimationClipUpgrader_Types.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\LitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\DecalProjectorEditor.Skin.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalRenderPipelineMaterialUpgrader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\GUIAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\BuildProcessors\URPPreprocessBuild.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\LiftGammaGainEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\Control.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\UniversalRenderPipelineGlobalSettingsPostprocessor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\AssetCallbacks\CreateFullscreenShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\RectSelector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\EditorUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\PixelPerfectCameraEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Selection\PointRectSelector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UniversalSpeedTree9MaterialUpgrader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\MaterialReferenceBuilder.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\ShadowsMidtonesHighlightsEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\Converters\RenderSettingsConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\Shapes\IShape.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Light2DEditorUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AnimationClipUpgrader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\UniversalFields.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\LayoutData.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.Output.Drawers.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\ProjectedTransform.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Converter\BuiltInToURP2DMaterialUpgrader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\CommandAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetVersion.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\VFXDecalURPOutput.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\AssetPostProcessors\AutodeskInteractiveMaterialImport.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Camera\UniversalRenderPipelineCameraUI.PresetInspector.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\View\EditablePathView.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RenderStateDataEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Shadows\CastingSourceDropDown.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ContextualMenuDispatcher.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\ReadonlyMaterialConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\Shaders\ParticlesLitShader.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\FreeformPathPresets.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\RenderPipelineConverter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\ShadingModels\LitGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\AssetCallbacks\CreateSpriteUnlitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditorTool\ScriptablePath.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Nodes\LightTextureNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\Converters.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\TonemappingEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\EditablePath\EditablePathController.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Targets\UniversalCanvasSubTarget.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Nodes\UniversalSampleBufferNode.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\UniversalMetadata.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\UpgradeCommon.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\SavedParameter.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\AssetCallbacks\CreateSpriteLitShaderGraph.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\SceneTemplates\URPBasicScenePipeline.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\BloomEditor.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShapeEditor\GUIFramework\ClickAction.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGUI\BaseShaderGUI.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\Shadows\ShadowProvider\ShadowShape2DProvider_ProperyDrawer.cs" />
    <Compile Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\AssetCallbacks\CreateSpriteCustomLitShaderGraph.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\Varyings.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\URPDecal\PassScreenSpace.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassVelocity.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitives\PassForward2D.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePoints\PassForward2D.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_widget_item.uxml" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Templates\ShaderPass.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassSelection.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_widget_main.uss" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\NewRendererFeature.cs.txt" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassSelection.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\URPDecal\PassDBuffer.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXLit.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleMeshes.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleLinesSW\PassForward2D.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\URPDecal\PassGBuffer.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_editor.uss" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\NewPostProcessRendererFeature.cs.txt" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\ShaderPassDecal.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassDepth.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\UnlitGBufferPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleLines\PassForward2D.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassDepthNormal.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleLinesSW.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleBasicCube.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\DecalMeshBiasTypeEnum.cs.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXDecal.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\LightBatchingDebugger\LayerBatch.uxml" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassDepthNormal.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\Shaders\ShadowsMidtonesHighlightsCurves.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassDepthOrMV.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Unity.RenderPipelines.Universal.Editor.asmdef" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassDepthOrMV.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\PBRGBufferPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\CanvasPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleCube.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassGBuffer.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXLitVaryings.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\UnlitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Includes\SpriteUnlitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\PBRForwardPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassDepth.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleLitPlanarPrimitive.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticlePoints.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\Pass.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\Shaders\TrackballEditor.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_editor.uxml" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleURPDecal.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassForward.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_widget.uxml" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleDecal.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\MotionVectorPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Decal\DecalPass.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Includes\SpriteNormalPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXVertexProbeSampling.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Includes\SpriteForwardPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\ShaderVariablesDecal.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\ShaderPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\SixWayForwardPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\ShadowCasterPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassVelocity.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_widget_main.uxml" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\LightingMetaPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleLinesHW.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\ShaderGraph\Includes\SpriteLitPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\PBR2DPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXSGSurfaceData.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\URPDecal\PassForwardEmissive.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshes\PassForward2D.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Overrides\Shaders\CurveBackground.shader" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleHexahedron\PassForward2D.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXPasses.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\SelectionPickingPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassShadowCaster.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Templates\SharedCode.template.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\LightBatchingDebugger\LightBatchingDebugger.uss" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXPassDepthCommonFragmentLit.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticleLitMesh.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticleMeshesLit\PassShadowCaster.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\DepthNormalsOnlyPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassGBuffer.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\RendererFeatures\NewPostProcessVolumeComponent.cs.txt" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\VFXDecalVaryings.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\ParticlePlanarPrimitivesLit\PassForward.template" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\2D\LightBatchingDebugger\LightBatchingDebugger.uxml" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\ShaderGraph\Includes\DepthOnlyPass.hlsl" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\Converter\converter_widget.uss" />
    <None Include="Library\PackageCache\com.unity.render-pipelines.universal@821b8547a8a5\Editor\VFXGraph\Shaders\Templates\VFXParticlePlanarPrimitive.template" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AMDModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AMDModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.Shared.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.Shared.csproj" />
    <ProjectReference Include="Unity.ShaderGraph.Editor.csproj" />
    <ProjectReference Include="Unity.Burst.Editor.csproj" />
    <ProjectReference Include="Unity.Mathematics.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Management.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Management.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Universal.2D.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.GPUDriven.Runtime.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="UnityEngine.TestRunner.csproj" />
    <ProjectReference Include="UnityEditor.TestRunner.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
