﻿<Project>
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <UseCommonOutputDirectory>true</UseCommonOutputDirectory>
    <OutputPath>Temp\bin\Debug\</OutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <LangVersion>9.0</LangVersion>
    <RootNamespace>UnityEngine.XR.Interaction.Toolkit</RootNamespace>
    <OutputType>Library</OutputType>
    <AssemblyName>Unity.XR.Interaction.Toolkit</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_6000_1_6;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;UNITY_POST_PROCESSING_STACK_V2;USE_STICK_CONTROL_THUMBSTICKS;ANIMATION_MODULE_PRESENT;PHYSICS2D_MODULE_PRESENT;BURST_PRESENT;XR_HANDS_1_1_OR_NEWER;XR_HANDS_1_3_OR_NEWER;XR_MANAGEMENT_4_0_OR_NEWER;OPENXR_1_6_OR_NEWER;OPENXR_1_7_OR_NEWER;OPENXR_1_10_OR_NEWER;XR_LEGACY_INPUT_HELPERS_2_1_OR_NEWER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>6000.1.6f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\Vector2MaterialPropertyAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\TouchscreenGestureInputControllerState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRBaseControllerInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRInteractionGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\InputActionUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\GestureRecognizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\UIInputModule.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\XRPokeFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Visuals\ICurveInteractionDataProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\ScriptableConstrainedBodyManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\RegisteredUIInteractorCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\TouchModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Visuals\XRInteractorReticleVisual.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARRotationInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\XR\XRRig.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\PinchGestureRecognizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\TrackedDeviceEventData.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\XRInputHapticImpulseProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\DistanceInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\XRGrabInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\Hands\HandExpressionName.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRBaseInputInteractor.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\IPokeStateDataProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportationMultiAnchorVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARBaseGestureInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AssemblyInfo.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\BurstGazeUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\XRBaseInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\XRBodyTransformations.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRSelectInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\XRSocketGrabTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\Visuals\IXRInteractableCustomReticle.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Evaluators\XRLastSelectedEvaluator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\ColorMaterialPropertyAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\TrackedDeviceModel.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Gravity\IGravityController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attachment\IAttachPointVelocityProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\RequireInterfaceAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Climbing\ClimbSettingsDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\ColorAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\SimulatedInputLayoutLoader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARAnnotationInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\TwoHandedGrabMoveProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\XRUIInputModule.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\BaseTeleportationInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputReaderUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\XRInputModalityManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\Hands\XRDeviceSimulatorHandsProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRHoverInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\GestureTouchesUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\LocomotionProvider.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\HapticImpulseCommandChannel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\PokeThresholdDatumProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\XRInteractionEvents.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\Primitives\Vector3AffordanceThemeDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Collections\NativeCurve.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\Vector3MaterialPropertyAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\ActionBasedController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\ITeleportationVolumeAnchorFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\TweenableVariableSynchronousBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\HapticsUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Interaction\XRInteractableUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\IConstrainedXRBodyManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\Hands\HandExpressionCapture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Jobs\ColorTweenJob.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attachment\AttachPointVelocityTracker.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportVolumeDestinationSettingsDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Climbing\ClimbTeleportInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\LocomotionSystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Audio\AudioAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\XRBaseGrabTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Casters\ICurveInteractionCaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Casters\IInteractionCaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceFloatValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\ConstrainedMoveProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TwistGesture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\XRDebugLineVisualizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\GrabMoveProvider.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\Primitives\ColorTweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportationAnchor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\CanvasTracker.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARScaleInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\XRInteractionManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\ContinuousMoveProvider.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\TriggerContactMonitor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\Primitives\Vector3TweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TwistGestureRecognizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\XR\InputHelpers.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\IXRBodyTransformation.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\BodyUI\FollowPresetDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\LocomotionPhase.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\InputFeatureUsageString.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\ARTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRBaseInputInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\IXRBodyPositionEvaluator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\ConstrainedMoveProvider.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\TouchscreenGestureInputController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\InputActionManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\Vector4MaterialPropertyAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\BaseAsyncAffordanceStateReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Jobs\TweenJobData.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\PointerModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Rendering\MaterialInstanceHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Turning\SnapTurnProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\XRLegacyGrabTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\XRMovableBody.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Select\IXRSelectFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\TeleportationMonitor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\DisposableManagerSingleton.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\SimulatedHandExpression.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\IAsyncAffordanceStateReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\BodyUI\HandMenu.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Gravity\GravityProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputButtonReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Jobs\ITweenJob.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\FloatAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TwoFingerDragGestureRecognizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARPlacementInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\ComponentLocatorUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\GestureTransformationUtility.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\BurstLerpUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\CharacterControllerDriver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceQuaternionValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactors\ARGestureInteractor.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\RegistrationList.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\BaseTeleportationInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\LocomotionMediator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TwoFingerDragGesture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARTranslationInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\ScreenSpaceRayPoseDriver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Layers\InteractionLayerSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\InteractorHandedness.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\XRInteractionManager.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Visuals\IXRCustomReticleProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\BlendShapeAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\XRHandSkeletonPokeDisplacer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRDeviceSimulatorSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Composites\FallbackComposite.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\ScreenSpacePinchScaleInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attachment\InteractionAttachController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Feedback\SimpleHapticFeedback.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\Primitives\ColorAffordanceThemeDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\FloatMaterialPropertyAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Casters\CurveInteractionCaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\XRTransformStabilizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\IXRSelectInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attributes\CanFocusMultipleAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\Gesture.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\HapticControlActionManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\IXRDropTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\Primitives\Vector4AffordanceThemeDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceInputTrackingStateValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TapGesture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Turning\ContinuousTurnProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\CanvasOptimizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\ActionBasedContinuousTurnProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRDirectInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Climbing\ClimbSettingsDatumProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\UIInputModule.Events.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRInteractionOverrideGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\EditorComponentLocatorUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\ActionBasedSnapTurnProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRControllerRecording.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\Gesture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\State\Provider\XRInteractableAffordanceStateProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\XRUIInputModule.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRRayProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\XRInteractionUpdateOrder.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\IXRPokeFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\TrackedDeviceModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRGazeInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\XRGeneralGrabTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRRayInteractor.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\XRInteractableSnapVolume.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Climbing\ClimbProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\XRInputDeviceHapticImpulseChannel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\InteractionState.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\XRHelpURLConstants.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRControllerRecorder.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\PokeStateData.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRInteractionSimulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\GazeTeleportationAnchorFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\ProjectPath.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Climbing\ClimbInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\InteractionState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\State\Data\AffordanceStateData.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\XRInputDeviceHapticImpulseProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Locomotion\LocomotionUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Interactions\SectorInteraction.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Climbing\ClimbSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\Hands\XRSimulatedHandState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\IXRHoverInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRBaseInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\XRSingleGrabFreeTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\DragGesture.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\NavigationModel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRScreenSpaceController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Comfort\TunnelingVignetteController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Casters\SphereInteractionCaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\XRHelpURLConstants.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attributes\XRTargetEvaluatorEnabledAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\ContinuousTurnProviderBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\State\Provider\XRInteractorAffordanceStateProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\DeviceBasedContinuousTurnProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\ContinuousMoveProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRSimulatedHMDState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\InputActionPropertyExtensions.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\UnderCameraBodyPositionEvaluator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\XRInputTrackingAggregator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRScaleValueProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\IXRActivateInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\UnityObjectReferenceCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRInteractionGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\IXRHapticImpulseChannelGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\IXRInteractionStrengthInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\IXRFilterList.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\XRPokeLogic.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Casters\InteractionCasterBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\XRBaseInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\QuaternionEulerAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\Vector2AffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\SmartTweenableVariables\SmartFollowVector3TweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\XR\GizmoHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\TweenableVariableBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\TouchscreenGestureInputLayoutLoader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attachment\IAttachPointVelocityTracker.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attachment\IFarAttachProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\XRGrabInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\HapticImpulseSingleChannelGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\BaseAffordanceStateReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\CardinalUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRRayInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\State\Data\AffordanceStateShortcuts.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\SmartTweenableVariables\SmartFollowQuaternionTweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceBoolValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\ActionBasedContinuousMoveProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\Visuals\XRTintInteractableVisual.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\DragGestureRecognizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\BurstPhysicsUtils.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Gaze\IXROverridesGazeAutoSelect.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\DragGesture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\Vector4AffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\ScreenSpaceSelectInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\GradientUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportVolumeDestinationSettings.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\IXRHapticImpulseProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Gaze\IXRAimAssist.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\IUIModelUpdater.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\LocomotionProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRTargetPriorityInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Collections\CircularBuffer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\SmallRegistrationList.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportVolumeDestinationSettingsDatumProperty.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRInteractionSimulatorLoader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRDeviceSimulatorLoader.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceVector3ValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRActivateInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\ISynchronousAffordanceStateReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRSimulatedControllerState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\DeviceBasedContinuousMoveProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Evaluators\IXRTargetEvaluatorLinkable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\Primitives\FloatTweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Rendering\MaterialPropertyBlockHelper.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\IXRHapticImpulseChannel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Jump\JumpProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Visuals\CurveVisualController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\Primitives\FloatAffordanceThemeDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attributes\CanSelectMultipleAttribute.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Pooling\LinkedPool.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\IXRInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRControllerRecorder.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\Primitives\Vector2AffordanceThemeDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\TouchscreenGestureInputLoader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Interaction\XRFilterUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRGroupMember.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\PinchGesture.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\XRInteractionEvents.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\IXRFocusInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRSocketInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportingEventArgs.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Attachment\IInteractionAttachController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Filters\XRBaseTargetFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceButtonReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\LazyFollow.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\XRBodyTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\DeviceBasedSnapTurnProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRInteractionStrengthInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\UI\ImageColorAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\XRInputDeviceHapticImpulseChannelGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\QuaternionAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TwoFingerDragGesture.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\PokeThresholdDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRBaseInteractor.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Hover\TouchscreenHoverFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Inputs\ScreenSpaceRotateInput.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\Audio\AudioAffordanceThemeDatum.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\TrackedDeviceGraphicRaycaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\OpenXR\OpenXRHapticImpulseChannel.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\BurstMathUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Filters\IXRTargetFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRDeviceSimulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\TapGestureRecognizer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRBaseController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\UI\TrackedDevicePhysicsRaycaster.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRControllerState.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Readers\XRInputDeviceVector2ValueReader.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactors\IARInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\Primitives\Vector4TweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactables\XRSimpleInteractable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\XRDualGrabFreeTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Interactables\ARSelectionInteractable.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\CharacterControllerBodyManipulator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Evaluators\XRTargetEvaluator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRSimulatorUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Jobs\FloatTweenJob.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\ExposedRegistrationList.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\XRPokeInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Visuals\IXRReticleDirectionProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Rendering\ColorGradientLineRendererAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\ScriptableSingletonCache.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\PokeThresholdData.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\Visuals\XRInteractorLineVisual.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AR\Gestures\GestureRecognizer.deprecated.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Feedback\SimpleAudioFeedback.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Primitives\Vector3AffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Poke\IMultiPokeStateDataProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\SnapTurnProviderBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRSimulatedController.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\FurthestTeleportationAnchorFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\HapticImpulseCommandChannelGroup.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\InteractionStrength\IXRInteractionStrengthFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\XR\SortingHelpers.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Gaze\XRGazeAssistance.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Filters\XRTargetFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Pooling\PooledObject.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Legacy\ContinuousMoveProviderBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Theme\BaseAffordanceTheme.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Haptics\HapticImpulsePlayer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\SimulatedDeviceLifecycleManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\IAffordanceStateReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRControllerState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\NearFarInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Rendering\MaterialHelperBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Interactors\IXRInteractor.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Controllers\XRControllerRecording.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\Transformation\UniformTransformScaleAffordanceReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\Receiver\BaseSynchronousAffordanceStateReceiver.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\Primitives\Vector2TweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportationProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\AffordanceSystem\State\Provider\BaseAffordanceStateProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\TweenableVariableAsyncBase.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRSimulatedHMD.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\LocomotionState.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\SimulatedHandExpressionManager.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Evaluators\XRDistanceEvaluator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Teleportation\TeleportationArea.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\Hands\XRDeviceSimulatorHandsSubsystem.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Target\Evaluators\XRAngleGazeEvaluator.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Movement\GrabMoveProvider.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Tweenables\Primitives\QuaternionTweenableVariable.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Filtering\Hover\IXRHoverFilter.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Transformers\IXRGrabTransformer.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Interaction\Layers\InteractionLayerMask.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Utilities\Curves\CurveUtility.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Locomotion\Gravity\GravityOverride.cs" />
    <Compile Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Inputs\Simulation\XRDeviceSimulator.deprecated.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Runtime\Unity.XR.Interaction.Toolkit.asmdef" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@56bff8827a7e\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@031a54704bff\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library\PackageCache\com.unity.collab-proxy@c854d1f7d97f\Lib\Editor\log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="UnityEngine.SpatialTracking.csproj" />
    <ProjectReference Include="UnityEngine.XR.LegacyInputHelpers.csproj" />
    <ProjectReference Include="Unity.InputSystem.csproj" />
    <ProjectReference Include="Unity.XR.CoreUtils.csproj" />
    <ProjectReference Include="Unity.XR.Management.csproj" />
    <ProjectReference Include="Unity.Mathematics.csproj" />
    <ProjectReference Include="Unity.Burst.csproj" />
    <ProjectReference Include="Unity.XR.Hands.csproj" />
    <ProjectReference Include="Unity.XR.OpenXR.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
