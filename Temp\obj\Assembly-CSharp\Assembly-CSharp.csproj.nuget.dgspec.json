{"format": 1, "restore": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-CSharp.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj", "projectName": "Assembly-AnimateGraphicMaterials-Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Assembly-AnimateGraphicMaterials-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-AnimateGraphicMaterials-Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj", "projectName": "Assembly-GraphicMaterialOverride", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Assembly-GraphicMaterialOverride.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Assembly-GraphicMaterialOverride\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj", "projectName": "PPv2URPConverters", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\PPv2URPConverters.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\PPv2URPConverters\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj", "projectName": "Unity.Burst", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Burst\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj", "projectName": "Unity.Burst.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Burst.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj", "projectName": "Unity.Collections", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Collections\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj", "projectName": "Unity.Collections.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Collections.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj", "projectName": "Unity.EditorCoroutines.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.EditorCoroutines.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj", "projectName": "Unity.InputSystem", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.InputSystem\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj", "projectName": "Unity.InputSystem.ForUI", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.ForUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.InputSystem.ForUI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj", "projectName": "Unity.InternalAPIEditorBridge.007", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.InternalAPIEditorBridge.007\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj", "projectName": "Unity.InternalAPIEngineBridge.007", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.InternalAPIEngineBridge.007\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj", "projectName": "Unity.Mathematics", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Mathematics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj", "projectName": "Unity.Mathematics.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Mathematics.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj", "projectName": "Unity.Multiplayer.Center.Common", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Multiplayer.Center.Common\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj", "projectName": "Unity.Multiplayer.Center.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Multiplayer.Center.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Multiplayer.Center.Common.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj", "projectName": "Unity.Performance.Profile-Analyzer.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Performance.Profile-Analyzer.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Performance.Profile-Analyzer.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj", "projectName": "Unity.PlasticSCM.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.PlasticSCM.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.PlasticSCM.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj", "projectName": "Unity.Rendering.LightTransport.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Rendering.LightTransport.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj", "projectName": "Unity.Rendering.LightTransport.Runtime", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Rendering.LightTransport.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "projectName": "Unity.RenderPipeline.Universal.ShaderLibrary", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipeline.Universal.ShaderLibrary\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj", "projectName": "Unity.RenderPipelines.Core.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rendering.LightTransport.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj", "projectName": "Unity.RenderPipelines.Core.Editor.Shared", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Core.Editor.Shared\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Core.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj", "projectName": "Unity.RenderPipelines.Core.Runtime.Shared", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Core.Runtime.Shared\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj", "projectName": "Unity.RenderPipelines.Core.ShaderLibrary", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.ShaderLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Core.ShaderLibrary\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj", "projectName": "Unity.RenderPipelines.GPUDriven.Runtime", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.GPUDriven.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Collections.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "projectName": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj", "projectName": "Unity.RenderPipelines.Universal.2D.Runtime", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Universal.2D.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj", "projectName": "Unity.RenderPipelines.Universal.Config.Runtime", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Universal.Config.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj", "projectName": "Unity.RenderPipelines.Universal.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Universal.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj", "projectName": "Unity.RenderPipelines.Universal.Runtime", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Universal.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj", "projectName": "Unity.RenderPipelines.Universal.Shaders", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Shaders.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.RenderPipelines.Universal.Shaders\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj", "projectName": "Unity.Rider.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Rider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Rider.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj", "projectName": "Unity.Searcher.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Searcher.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj", "projectName": "Unity.Settings.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Settings.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj", "projectName": "Unity.ShaderGraph.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.ShaderGraph.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Searcher.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Utilities.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Utilities.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Utilities.csproj", "projectName": "Unity.ShaderGraph.Utilities", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.ShaderGraph.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.ShaderGraph.Utilities\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.TestTools.CodeCoverage.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Model", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "projectName": "Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj", "projectName": "Unity.TextMeshPro", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.TextMeshPro\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj", "projectName": "Unity.TextMeshPro.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.TextMeshPro.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Core.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.TextMeshPro.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj", "projectName": "Unity.Timeline", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Timeline\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj", "projectName": "Unity.Timeline.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Timeline.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Timeline.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj", "projectName": "Unity.Tutorials.Core", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Tutorials.Core\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj", "projectName": "Unity.Tutorials.Core.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.Tutorials.Core.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.EditorCoroutines.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEditorBridge.007.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InternalAPIEngineBridge.007.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Settings.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Tutorials.Core.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj", "projectName": "Unity.VisualStudio.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.VisualStudio.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.VisualStudio.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj", "projectName": "Unity.XR.CoreUtils", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.CoreUtils\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj", "projectName": "Unity.XR.CoreUtils.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.CoreUtils.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj", "projectName": "Unity.XR.Hands", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Hands\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj", "projectName": "Unity.XR.Hands.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Hands.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj", "projectName": "Unity.XR.Hands.Samples.VisualizerSample", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.Samples.VisualizerSample.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Hands.Samples.VisualizerSample\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Analytics.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Analytics.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Analytics.Hooks.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Analytics.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj", "projectName": "Unity.XR.Interaction.Toolkit", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Burst.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.Hands", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Samples.Hands\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Hands.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.Hands.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Samples.Hands.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.Mathematics.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Interaction.Toolkit.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj", "projectName": "Unity.XR.Management", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Management\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj", "projectName": "Unity.XR.Management.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.Management.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj", "projectName": "Unity.XR.OpenXR", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj", "projectName": "Unity.XR.OpenXR.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.CoreUtils.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj", "projectName": "Unity.XR.OpenXR.Features.ConformanceAutomation", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.ConformanceAutomation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.ConformanceAutomation\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj", "projectName": "Unity.XR.OpenXR.Features.MetaQuestSupport", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.MetaQuestSupport\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj", "projectName": "Unity.XR.OpenXR.Features.MetaQuestSupport.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.MetaQuestSupport.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.Management.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj", "projectName": "Unity.XR.OpenXR.Features.OculusQuestSupport", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.OculusQuestSupport\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.MetaQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj", "projectName": "Unity.XR.OpenXR.Features.OculusQuestSupport.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.OculusQuestSupport.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.OculusQuestSupport.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.csproj", "projectName": "Unity.XR.OpenXR.Features.RuntimeDebugger", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.RuntimeDebugger\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj", "projectName": "Unity.XR.OpenXR.Features.RuntimeDebugger.Editor", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\Unity.XR.OpenXR.Features.RuntimeDebugger.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Editor.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.XR.OpenXR.Features.RuntimeDebugger.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj", "projectName": "UnityEditor.SpatialTracking", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEditor.SpatialTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEditor.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj", "projectName": "UnityEditor.XR.LegacyInputHelpers", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.XR.LegacyInputHelpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEditor.XR.LegacyInputHelpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.InputSystem.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj", "projectName": "UnityEngine.SpatialTracking", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEngine.SpatialTracking\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEngine.UI\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj", "projectName": "UnityEngine.XR.LegacyInputHelpers", "projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.XR.LegacyInputHelpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\Temp\\obj\\UnityEngine.XR.LegacyInputHelpers\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEditor.UI.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.SpatialTracking.csproj"}, "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj": {"projectPath": "C:\\Users\\<USER>\\Vr-Archi-Stage-01\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100\\RuntimeIdentifierGraph.json"}}}}}