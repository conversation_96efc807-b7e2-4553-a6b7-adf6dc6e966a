> View the entire tutorial on [What Up Games](https://www.whatupgames.com).

#  Create an runtime inventory UI with UI Toolkit

A great UI is one of the most important parts of any game. It needs to be responsive and intuitive while looking awesome. In this tutorial, you’ll learn the basics of Unity’s new UI system, called UI Toolkit, by creating an in-game inventory system where your player can drag and drop items to move them around. 

![Example](final.gif)

## Learning Outcomes 
What is  great about learning UI Toolkit is you can apply the same skills to create custom UI and extensions for the Unity Editor, runtime debugging tools, and runtime UI for your games. At the end of this tutorial you will be able to:

- Design UIs by using the UI Builder tool.
- Design UIs through C# and USS (for styling).
- Instantiate your UI at runtime.
- Manipulate VisualElement styles at runtime, including setting a new position.
- Register and handle events for the UI. 

## Prerequisites

1. You need [Unity 2020.2.1f1](https://unity3d.com/get-unity/download) or later to follow along with this tutorial.
2. This tutorial assumes you have basic knowledge of Unity and intermediate knowledge of C#.
3. Download the starter project from the [GitHub repository](https://github.com/Yecats/GameDevTutorials).

## Resources
This project uses assets from Game-Icons.net and inspiration from from Wenrexa's free UI Kit #3 for the design.

1. [Game-Icons.net](http://game-icons.net/)
2. [Wenrexa: Free UI KIT #3](https://wenrexa.itch.io/ui-different03)


