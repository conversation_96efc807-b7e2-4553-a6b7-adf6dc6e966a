{"MonoBehaviour": {"m_Enabled": true, "m_EditorHideFlags": 0, "m_Name": "BuilderDocument", "m_EditorClassIdentifier": "UnityEditor.UIBuilderModule:Unity.UI.Builder:BuilderDocument", "m_SavedBuilderUxmlToThemeStyleSheetList": [], "m_CurrentCanvasTheme": 4, "m_CurrentCanvasThemeStyleSheetReference": {"fileID": -4733365628477956816, "guid": "56ace22954d8c084a9420b4ce8346a7c", "type": 3}, "m_CodePreviewVisible": true, "m_OpenUXMLFiles": [{"m_OpenUSSFiles": [{"m_StyleSheet": {"fileID": 7433441132597879392, "guid": "454825772093c7840be3d857971743dd", "type": 3}, "m_ContentHash": -1451740018, "m_UssPreview": ".container {\r\n    flex-grow: 1;\r\n    width: auto;\r\n    height: auto;\r\n    max-width: none;\r\n    max-height: none;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n.inventory {\r\n    flex-grow: 1;\r\n    max-width: 50%;\r\n    max-height: 75%;\r\n    width: 50%;\r\n    height: 75%;\r\n    background-color: rgb(28, 32, 36);\r\n    border-top-color: rgb(255, 197, 0);\r\n    border-top-width: 5px;\r\n    padding-left: 10px;\r\n    padding-right: 10px;\r\n    padding-top: 10px;\r\n    padding-bottom: 10px;\r\n}\r\n\r\n.header {\r\n    flex-grow: 0;\r\n    font-size: 24px;\r\n    color: rgb(255, 197, 0);\r\n    border-bottom-color: rgb(64, 74, 82);\r\n    border-bottom-width: 1px;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.slotsContainer {\r\n    -unity-background-image-tint-color: rgba(255, 255, 255, 0);\r\n    flex-grow: 1;\r\n    margin-top: 0;\r\n    padding-top: 10px;\r\n    flex-wrap: wrap;\r\n    flex-direction: row;\r\n}\r\n\r\n.slotContainer {\r\n    width: 128px;\r\n    height: 128px;\r\n    margin: 5px;\r\n    border-left-color: rgb(48, 48, 48);\r\n    border-right-color: rgb(48, 48, 48);\r\n    border-top-color: rgb(48, 48, 48);\r\n    border-bottom-color: rgb(48, 48, 48);\r\n    background-color: rgb(9, 19, 21);\r\n    background-image: url('/Assets/WUG/Sprites/ItemSlotBackground.png');\r\n    border-top-left-radius: 3px;\r\n    border-bottom-left-radius: 3px;\r\n    border-top-right-radius: 3px;\r\n    border-bottom-right-radius: 3px;\r\n}\r\n\r\n.slotIcon {\r\n    flex-shrink: 0;\r\n    flex-grow: 1;\r\n    padding: 15px;\r\n}\r\n", "m_OldPath": "Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/WUG/UI/Inventory.uss"}], "m_OpenendVisualTreeAssetOldPath": "Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/WUG/UI/Inventory.uxml", "m_UxmlPreview": "<ui:UXML xmlns:ui=\"UnityEngine.UIElements\" xmlns:uie=\"UnityEditor.UIElements\" editor-extension-mode=\"False\">\r\n    <Style src=\"project://database/Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/WUG/UI/Inventory.uss?fileID=7433441132597879392&amp;guid=454825772093c7840be3d857971743dd&amp;type=3#Inventory\" />\r\n    <ui:VisualElement name=\"Container\" class=\"container\">\r\n        <ui:VisualElement name=\"Inventory\" class=\"inventory\">\r\n            <ui:Label text=\"Inventory\" display-tooltip-when-elided=\"true\" name=\"Header\" class=\"header\" />\r\n            <ui:VisualElement name=\"SlotContainer\" class=\"slotsContainer\" style=\"align-items: center; justify-content: center; flex-direction: row; margin-top: 0; margin-left: 0; margin-right: 0; margin-bottom: 0; flex-shrink: 1;\" />\r\n        </ui:VisualElement>\r\n        <ui:VisualElement name=\"GhostIcon\" style=\"visibility: hidden; position: absolute; width: 128px; height: 128px; background-image: none;\" />\r\n    </ui:VisualElement>\r\n</ui:UXML>\r\n", "m_ContentHash": 123452390, "m_VisualTreeAssetRef": {"fileID": 9197481963319205126, "guid": "398c6f54d2157db49b2b77bd35f612fe", "type": 3}, "m_ActiveStyleSheet": {"instanceID": 0}, "m_Settings": {"UxmlGuid": "398c6f54d2157db49b2b77bd35f612fe", "UxmlPath": "Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/WUG/UI/Inventory.uxml", "CanvasX": 0, "CanvasY": 0, "CanvasWidth": 350, "CanvasHeight": 450, "MatchGameView": false, "ZoomScale": 1.0, "PanOffset": {"x": 20.0, "y": 20.0}, "ColorModeBackgroundOpacity": 1.0, "ImageModeCanvasBackgroundOpacity": 1.0, "CameraModeCanvasBackgroundOpacity": 1.0, "EnableCanvasBackground": false, "CanvasBackgroundMode": 0, "CanvasBackgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "CanvasBackgroundImage": {"instanceID": 0}, "CanvasBackgroundImageScaleMode": 1, "CanvasBackgroundCameraName": ""}, "m_OpenSubDocumentParentIndex": -1, "m_OpenSubDocumentParentSourceTemplateAssetIndex": -1}], "m_ActiveOpenUXMLFileIndex": 0}}