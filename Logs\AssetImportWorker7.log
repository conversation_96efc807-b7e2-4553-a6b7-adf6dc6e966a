Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.6f1 (d64b1a599cad) revision 14043930'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'fr' Physical Memory: 32471 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-18T17:07:41Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.6f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
C:/Users/<USER>/Vr-Archi-Stage-01
-logFile
Logs/AssetImportWorker7.log
-srvPort
60187
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Vr-Archi-Stage-01
C:/Users/<USER>/Vr-Archi-Stage-01
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [46168]  Target information:

Player connection [46168]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 532843530 [EditorId] 532843530 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [46168]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 532843530 [EditorId] 532843530 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [46168]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 532843530 [EditorId] 532843530 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [46168]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 532843530 [EditorId] 532843530 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [46168]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 532843530 [EditorId] 532843530 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-BKJOV3J) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [46168] Host joined multi-casting on [***********:54997]...
Player connection [46168] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 9.26 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.6f1 (d64b1a599cad)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Vr-Archi-Stage-01/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 4060 Laptop GPU (ID=0x28a0)
    Vendor:          NVIDIA
    VRAM:            7957 MB
    App VRAM Budget: 7189 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56704
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.6f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001463 seconds.
- Loaded All Assemblies, in  0.300 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.318 seconds
Domain Reload Profiling: 617ms
	BeginReloadAssembly (101ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (122ms)
		LoadAssemblies (100ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (120ms)
			TypeCache.Refresh (118ms)
				TypeCache.ScanAssembly (108ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (318ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (279ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (85ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (49ms)
			ProcessInitializeOnLoadAttributes (100ms)
			ProcessInitializeOnLoadMethodAttributes (41ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Assembly 'Assets/AR-House_Arcticture-unity-master/Library/ScriptAssemblies/VuforiaEditorScripts.dll' will not be loaded due to errors:
Unable to resolve reference 'Vuforia.UnityExtensions.Editor'. Is the assembly missing or incompatible with the current platform?
Reference validation can be disabled in the Plugin Inspector.
TypeCache is unable to load attribute info on method UnityEngine.Networking.ConnectionSimulatorConfig:.ctor (int,int,int,int,single). Are you missing a reference?
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
scripting_class_is_subclass_of was called with a NULL parameter. This will crash the game if it happens outside of the editor.
Unloading broken assembly Assets/AR-House_Arcticture-unity-master/Library/ScriptAssemblies/Unity.PackageManagerUI.Editor.dll, this assembly can cause crashes in the runtime
Unloading broken assembly Assets/AR-House_Arcticture-unity-master/Library/PlayerDataCache/Android/Data/Managed/UnityEngine.UNETModule.dll, this assembly can cause crashes in the runtime
- Loaded All Assemblies, in  0.679 seconds
Assembly DLL name is reserved for internal use: Assets/AR-House_Arcticture-unity-master/Library/PlayerDataCache/Android/Data/Managed/mscorlib.dll (did files generated by a build accidentally end up in your Assets/ folder?)
Could not load signature of UnityEngine.Networking.NetworkManager:get_connectionConfig due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_globalConfig due to: Could not resolve type with token 01000010 from typeref (expected class 'UnityEngine.Networking.GlobalConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.GlobalConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_channels due to: Could not resolve type with token 01000011 from typeref (expected class 'UnityEngine.Networking.QosType' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.QosType member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchCreate due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchList due to: Could not resolve type with token 01000015 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfoSnapshot' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfoSnapshot member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchJoined due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_connectionConfig due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_globalConfig due to: Could not resolve type with token 01000010 from typeref (expected class 'UnityEngine.Networking.GlobalConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.GlobalConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_channels due to: Could not resolve type with token 01000011 from typeref (expected class 'UnityEngine.Networking.QosType' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.QosType member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchCreate due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchList due to: Could not resolve type with token 01000015 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfoSnapshot' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfoSnapshot member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchJoined due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkMigrationManager:get_matchInfo due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkMigrationManager:Initialize due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Assembly DLL name is reserved for internal use: Assets/AR-House_Arcticture-unity-master/Library/PlayerDataCache/Android/Data/Managed/UnityEngine.dll (did files generated by a build accidentally end up in your Assets/ folder?)
Refreshing native plugins compatible for Editor in 5.06 ms, found 9 plugins.
Could not load signature of UnityEngine.Networking.NetworkManager:get_connectionConfig due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_globalConfig due to: Could not resolve type with token 01000010 from typeref (expected class 'UnityEngine.Networking.GlobalConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.GlobalConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_channels due to: Could not resolve type with token 01000011 from typeref (expected class 'UnityEngine.Networking.QosType' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.QosType member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchCreate due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchList due to: Could not resolve type with token 01000015 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfoSnapshot' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfoSnapshot member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchJoined due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkMigrationManager:get_matchInfo due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkMigrationManager:Initialize due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_connectionConfig due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_globalConfig due to: Could not resolve type with token 01000010 from typeref (expected class 'UnityEngine.Networking.GlobalConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.GlobalConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:get_channels due to: Could not resolve type with token 01000011 from typeref (expected class 'UnityEngine.Networking.QosType' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.QosType member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartServer due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartClient due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 0100000f from typeref (expected class 'UnityEngine.Networking.ConnectionConfig' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.ConnectionConfig member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:StartHost due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchCreate due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchList due to: Could not resolve type with token 01000015 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfoSnapshot' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfoSnapshot member:(null)
Could not load signature of UnityEngine.Networking.NetworkManager:OnMatchJoined due to: Could not resolve type with token 01000013 from typeref (expected class 'UnityEngine.Networking.Match.MatchInfo' in assembly 'UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null') assembly:UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null type:UnityEngine.Networking.Match.MatchInfo member:(null)
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 6.59 ms, found 9 plugins.
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.093 seconds
Domain Reload Profiling: 1768ms
	BeginReloadAssembly (136ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (458ms)
		LoadAssemblies (291ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (245ms)
			TypeCache.Refresh (169ms)
				TypeCache.ScanAssembly (153ms)
			BuildScriptInfoCaches (61ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1093ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (960ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (699ms)
			ProcessInitializeOnLoadMethodAttributes (148ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 4.72 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 267 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6064 unused Assets / (8.8 MB). Loaded Objects now: 7056.
Memory consumption went from 285.0 MB to 276.2 MB.
Total: 9.444300 ms (FindLiveObjects: 0.496400 ms CreateObjectMapping: 0.409700 ms MarkObjects: 4.620800 ms  DeleteObjects: 3.914900 ms)

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 74.62 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5852 unused Assets / (5.7 MB). Loaded Objects now: 6643.
Memory consumption went from 180.7 MB to 175.0 MB.
Total: 76.664300 ms (FindLiveObjects: 0.958500 ms CreateObjectMapping: 0.535200 ms MarkObjects: 48.746400 ms  DeleteObjects: 26.422300 ms)

Prepare: number of updated asset objects reloaded= 13
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.664 seconds
Refreshing native plugins compatible for Editor in 33.85 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
System.NullReferenceException: Object reference not set to an instance of an object
  at UnityEditor.XR.Interaction.Toolkit.Analytics.XRIAnalytics..cctor () [0x00000] in .\Library\PackageCache\com.unity.xr.interaction.toolkit@9b07900cb163\Editor\Analytics\XRIAnalytics.cs:32 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to get package versions: Cannot connect to Unity Package Manager local server
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:UpdatePackageVersions () (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:59)
Unity.XR.CoreUtils.Editor.PackageVersionUtility:GetPackageVersion (string) (at ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs:77)
UnityEditor.XR.Interaction.Toolkit.Samples.Hands.Editor.HandsSampleProjectValidation:.cctor () (at Assets/Samples/XR Interaction Toolkit/3.1.1/Hands Interaction Demo/Editor/Scripts/HandsSampleProjectValidation.cs:34)
System.Reflection.RuntimeMethodInfo:Invoke (object,System.Reflection.BindingFlags,System.Reflection.Binder,object[],System.Globalization.CultureInfo)
System.Reflection.MethodBase:Invoke (object,object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes ()

(Filename: ./Library/PackageCache/com.unity.xr.core-utils@5b282bc7378d/Editor/ProjectValidation/PackageVersionUtility.cs Line: 59)

Mono: successfully reloaded assembly
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  2.633 seconds
Domain Reload Profiling: 4289ms
	BeginReloadAssembly (486ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (124ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (1003ms)
		LoadAssemblies (779ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (452ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (391ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (2635ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2157ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (386ms)
			ProcessInitializeOnLoadAttributes (1368ms)
			ProcessInitializeOnLoadMethodAttributes (342ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 28.96 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 74 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5917 unused Assets / (5.9 MB). Loaded Objects now: 6652.
Memory consumption went from 195.1 MB to 189.2 MB.
Total: 24.801000 ms (FindLiveObjects: 2.049900 ms CreateObjectMapping: 2.049700 ms MarkObjects: 12.869300 ms  DeleteObjects: 7.830700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.67 ms, found 9 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 45 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5856 unused Assets / (7.4 MB). Loaded Objects now: 6649.
Memory consumption went from 169.6 MB to 162.2 MB.
Total: 10.499400 ms (FindLiveObjects: 0.659400 ms CreateObjectMapping: 0.427600 ms MarkObjects: 5.483700 ms  DeleteObjects: 3.927200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 473473.138982 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/PanelSettings.asset
  artifactKey: Guid(62c0f3d1da4986243a0f35ace904a7f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/Assets/UI Toolkit/PanelSettings.asset using Guid(62c0f3d1da4986243a0f35ace904a7f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '267584da0e34cce0737427af6c4e152a') in 0.010205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 124.696708 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/ClusterInputManager.asset
  artifactKey: Guid(6a510785dacbbe1459fa637f53d0b7fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/ClusterInputManager.asset using Guid(6a510785dacbbe1459fa637f53d0b7fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a41fe9d363fae972e41cb07a01356a25') in 0.0008445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/PackageManagerSettings.asset
  artifactKey: Guid(d67505838a9876548a6f140e7d8a3bf2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/PackageManagerSettings.asset using Guid(d67505838a9876548a6f140e7d8a3bf2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d6badef0502b4aa9f49134e2808f726') in 0.0009608 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/ProjectSettings.asset
  artifactKey: Guid(b9e80fa1a4b04134ab145df69a330031) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/ProjectSettings.asset using Guid(b9e80fa1a4b04134ab145df69a330031) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66704ceb6b3c244b4327cb95f6a4cac2') in 0.0013063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/InputManager.asset
  artifactKey: Guid(fdd9c79aa920e4d4cb457455de432e5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/InputManager.asset using Guid(fdd9c79aa920e4d4cb457455de432e5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '755ff48c721f4445e384163191199494') in 0.0009832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/PresetManager.asset
  artifactKey: Guid(0f686a4dc17542a4b944dc59968d5c83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/PresetManager.asset using Guid(0f686a4dc17542a4b944dc59968d5c83) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7862086d8351ea1b3159c6fd25e5ccc') in 0.0010372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/VersionControlSettings.asset
  artifactKey: Guid(512e41e57f35a3e46a57f6c60dee6621) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/VersionControlSettings.asset using Guid(512e41e57f35a3e46a57f6c60dee6621) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6afcab5648e6fe187b52997d297c21af') in 0.0008913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/XRSettings.asset
  artifactKey: Guid(b6bd4252ba7f650449b1bd7daae197ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/XRSettings.asset using Guid(b6bd4252ba7f650449b1bd7daae197ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Failed to load 'C:/Users/<USER>/Vr-Archi-Stage-01/Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/XRSettings.asset'. File may be corrupted or was serialized with a newer version of Unity.
 -> (artifact id: '6fdc19c38f3e61d6092dd37f0cd51536') in 0.0010582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/TagManager.asset
  artifactKey: Guid(c7d83170bcb323e438bd912e0b7484dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/TagManager.asset using Guid(c7d83170bcb323e438bd912e0b7484dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ac72cbf5ca37700844433b2a8f7b7afe') in 0.0012561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/EditorSettings.asset
  artifactKey: Guid(e15d7887b1e7d8c48a635968da11f025) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/EditorSettings.asset using Guid(e15d7887b1e7d8c48a635968da11f025) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '702a269fe093c9b20aba0309d49ec165') in 0.0011442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/UnityConnectSettings.asset
  artifactKey: Guid(73b226f16df729246bc1af281bac290e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-starter/ProjectSettings/UnityConnectSettings.asset using Guid(73b226f16df729246bc1af281bac290e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ffc60d4b69a8e289c266602263aafe2') in 0.0014525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 15.184632 seconds.
  path: Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/ProjectSettings/EditorSettings.asset
  artifactKey: Guid(91d08f59f0201bb4fb9983ab681017f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/create-a-runtime-inventory-with-UI-Toolkit-main/Runtime-Inventory-final/ProjectSettings/EditorSettings.asset using Guid(91d08f59f0201bb4fb9983ab681017f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6cac7943e71e96ad4f7a94f28ac51f8d') in 0.0012037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0